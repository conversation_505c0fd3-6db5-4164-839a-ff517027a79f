/**
 * Integration test for Static Database Search Functionality
 *
 * Tests the complete user journey of searching static disc databases
 */

import React from "react";
import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { EnhancedDatabaseSearchTab } from "@/components/import/EnhancedDatabaseSearchTab";
import { setupTest, cleanupTest, testAccessibility, createMockFunction } from "../utils/testUtils";

// Mock the static database API
vi.mock("@/lib/staticDatabaseAPI", () => ({
  searchStaticDatabase: vi.fn(),
  getStaticDatabaseProviders: vi.fn(),
  getStaticDatabaseManufacturers: vi.fn(),
  getStaticDatabaseCategories: vi.fn(),
}));

// Mock the DiscIt API
vi.mock("@/lib/discDatabaseAPI", () => ({
  searchAndTransformDiscItAPI: vi.fn(),
  getDiscItBrands: vi.fn(),
  getDiscItCategories: vi.fn(),
  getDiscItStabilityOptions: vi.fn(),
}));

import {
  searchStaticDatabase,
  getStaticDatabaseProviders,
  getStaticDatabaseManufacturers,
  getStaticDatabaseCategories,
} from "@/lib/staticDatabaseAPI";

import {
  searchAndTransformDiscItAPI,
  getDiscItBrands,
  getDiscItCategories,
  getDiscItStabilityOptions,
} from "@/lib/discDatabaseAPI";

describe("Static Database Search Integration", () => {
  const mockOnDiscSelect = createMockFunction<(discs: any[]) => void>();

  beforeEach(() => {
    setupTest();

    // Setup default mocks
    (getStaticDatabaseProviders as any).mockReturnValue([
      {
        provider: "pdga",
        name: "PDGA Approved",
        description: "Official PDGA approved discs",
        lastUpdated: "2025-01-16",
        totalDiscs: 100,
        version: "1.0.0",
      },
      {
        provider: "community",
        name: "Community Database",
        description: "Community-curated flight numbers",
        lastUpdated: "2025-01-16",
        totalDiscs: 200,
        version: "1.0.0",
      },
    ]);

    (getStaticDatabaseManufacturers as any).mockResolvedValue({
      success: true,
      data: ["Innova", "Discraft", "Dynamic Discs"],
    });

    (getStaticDatabaseCategories as any).mockResolvedValue({
      success: true,
      data: ["Distance Driver", "Fairway Driver", "Midrange", "Putter"],
    });

    (getDiscItBrands as any).mockResolvedValue({
      success: true,
      data: ["Innova", "Discraft", "Dynamic Discs"],
    });

    (getDiscItCategories as any).mockResolvedValue({
      success: true,
      data: ["Distance Driver", "Fairway Driver", "Midrange", "Putter"],
    });

    (getDiscItStabilityOptions as any).mockResolvedValue({
      success: true,
      data: ["Overstable", "Stable", "Understable"],
    });

    (searchStaticDatabase as any).mockResolvedValue({
      success: true,
      data: [
        {
          id: "test-1",
          manufacturer: "Innova",
          mold: "Destroyer",
          plasticType: "Champion",
          weight: 175,
          flightNumbers: { speed: 12, glide: 5, turn: -1, fade: 3 },
          notes: "Imported from PDGA Approved Discs database",
        },
      ],
    });

    (searchAndTransformDiscItAPI as any).mockResolvedValue({
      success: true,
      data: [
        {
          id: "discit-1",
          manufacturer: "Discraft",
          mold: "Buzzz",
          plasticType: "ESP",
          weight: 177,
          flightNumbers: { speed: 5, glide: 4, turn: -1, fade: 1 },
          notes: "Imported from DiscIt API",
        },
      ],
    });
  });

  afterEach(cleanupTest);

  describe("Provider Selection", () => {
    it("renders all database provider tabs", async () => {
      render(<EnhancedDatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

      // Check for provider tabs
      expect(screen.getByRole("tab", { name: /DiscIt API/ })).toBeInTheDocument();
      expect(screen.getByRole("tab", { name: /PDGA Approved/ })).toBeInTheDocument();
      expect(screen.getByRole("tab", { name: /Community Database/ })).toBeInTheDocument();
      expect(screen.getByRole("tab", { name: /Manufacturer Collections/ })).toBeInTheDocument();
    });

    it("switches between providers correctly", async () => {
      const user = userEvent.setup();
      render(<EnhancedDatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

      // Click on PDGA tab
      const pdgaTab = screen.getByRole("tab", { name: /PDGA Approved/ });
      await user.click(pdgaTab);

      // Should show PDGA description
      await waitFor(() => {
        expect(screen.getByText("Official PDGA approved discs for tournament play")).toBeInTheDocument();
      });

      // Click on Community tab
      const communityTab = screen.getByRole("tab", { name: /Community Database/ });
      await user.click(communityTab);

      // Should show Community description
      await waitFor(() => {
        expect(screen.getByText("Community-curated flight numbers and reviews")).toBeInTheDocument();
      });
    });
  });

  describe("Search Functionality", () => {
    it("performs search with static database", async () => {
      const user = userEvent.setup();
      render(<EnhancedDatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

      // Switch to PDGA provider
      const pdgaTab = screen.getByRole("tab", { name: /PDGA Approved/ });
      await user.click(pdgaTab);

      // Enter search query
      const searchInput = screen.getByLabelText("Search Query");
      await user.type(searchInput, "Destroyer");

      // Click search button
      const searchButton = screen.getByRole("button", { name: /Search Discs/ });
      await user.click(searchButton);

      // Wait for search to complete
      await waitFor(() => {
        expect(searchStaticDatabase).toHaveBeenCalledWith(
          expect.objectContaining({
            provider: "pdga",
            limit: 50,
          })
        );
      });

      // Should show results
      await waitFor(() => {
        expect(screen.getByText("Search Results")).toBeInTheDocument();
        expect(screen.getByText("Found 1 discs")).toBeInTheDocument();
        expect(screen.getByText("Innova Destroyer")).toBeInTheDocument();
      });
    });

    it("performs search with DiscIt API", async () => {
      const user = userEvent.setup();
      render(<EnhancedDatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

      // DiscIt API should be selected by default
      expect(screen.getByText("Live disc database with comprehensive flight data")).toBeInTheDocument();

      // Enter search query
      const searchInput = screen.getByLabelText("Search Query");
      await user.type(searchInput, "Buzzz");

      // Click search button
      const searchButton = screen.getByRole("button", { name: /Search Discs/ });
      await user.click(searchButton);

      // Wait for search to complete
      await waitFor(() => {
        expect(searchAndTransformDiscItAPI).toHaveBeenCalledWith(
          expect.objectContaining({
            limit: 50,
          })
        );
      });

      // Should show results
      await waitFor(() => {
        expect(screen.getByText("Search Results")).toBeInTheDocument();
        expect(screen.getByText("Found 1 discs")).toBeInTheDocument();
        expect(screen.getByText("Discraft Buzzz")).toBeInTheDocument();
      });
    });

    it("applies filters correctly", async () => {
      const user = userEvent.setup();
      render(<EnhancedDatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

      // Switch to community provider
      const communityTab = screen.getByRole("tab", { name: /Community Database/ });
      await user.click(communityTab);

      // Wait for filter options to load
      await waitFor(() => {
        expect(getStaticDatabaseManufacturers).toHaveBeenCalledWith("community");
      });

      // Select manufacturer filter
      const manufacturerSelect = screen.getByRole("combobox", { name: /Manufacturer/ });
      await user.click(manufacturerSelect);
      
      // Note: In a real test, we'd need to handle the Select component properly
      // For now, we'll just verify the search is called
      const searchButton = screen.getByRole("button", { name: /Search Discs/ });
      await user.click(searchButton);

      await waitFor(() => {
        expect(searchStaticDatabase).toHaveBeenCalledWith(
          expect.objectContaining({
            provider: "community",
            limit: 50,
          })
        );
      });
    });
  });

  describe("Disc Selection and Import", () => {
    it("allows selecting and importing discs", async () => {
      const user = userEvent.setup();
      render(<EnhancedDatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

      // Perform search first
      const searchButton = screen.getByRole("button", { name: /Search Discs/ });
      await user.click(searchButton);

      // Wait for results
      await waitFor(() => {
        expect(screen.getByText("Discraft Buzzz")).toBeInTheDocument();
      });

      // Select a disc
      const discCard = screen.getByText("Discraft Buzzz").closest("div");
      expect(discCard).toBeInTheDocument();
      await user.click(discCard!);

      // Should show import button
      await waitFor(() => {
        expect(screen.getByRole("button", { name: /Import Selected \(1\)/ })).toBeInTheDocument();
      });

      // Click import
      const importButton = screen.getByRole("button", { name: /Import Selected \(1\)/ });
      await user.click(importButton);

      // Should call onDiscSelect with selected discs
      expect(mockOnDiscSelect).toHaveBeenCalledWith([
        expect.objectContaining({
          manufacturer: "Discraft",
          mold: "Buzzz",
        }),
      ]);
    });

    it("supports select all functionality", async () => {
      const user = userEvent.setup();
      
      // Mock multiple results
      (searchAndTransformDiscItAPI as any).mockResolvedValue({
        success: true,
        data: [
          {
            id: "discit-1",
            manufacturer: "Discraft",
            mold: "Buzzz",
            plasticType: "ESP",
            weight: 177,
            flightNumbers: { speed: 5, glide: 4, turn: -1, fade: 1 },
          },
          {
            id: "discit-2",
            manufacturer: "Innova",
            mold: "Destroyer",
            plasticType: "Champion",
            weight: 175,
            flightNumbers: { speed: 12, glide: 5, turn: -1, fade: 3 },
          },
        ],
      });

      render(<EnhancedDatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

      // Perform search
      const searchButton = screen.getByRole("button", { name: /Search Discs/ });
      await user.click(searchButton);

      // Wait for results
      await waitFor(() => {
        expect(screen.getByText("Found 2 discs")).toBeInTheDocument();
      });

      // Click select all
      const selectAllButton = screen.getByRole("button", { name: /Select All/ });
      await user.click(selectAllButton);

      // Should show import button with count
      await waitFor(() => {
        expect(screen.getByRole("button", { name: /Import Selected \(2\)/ })).toBeInTheDocument();
      });
    });
  });

  describe("Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(
        <EnhancedDatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />
      );
      await testAccessibility(container);
    });

    it("supports keyboard navigation", async () => {
      const user = userEvent.setup();
      render(<EnhancedDatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

      // Tab through provider tabs
      await user.tab();
      expect(screen.getByRole("tab", { name: /DiscIt API/ })).toHaveFocus();

      await user.tab();
      expect(screen.getByRole("tab", { name: /PDGA Approved/ })).toHaveFocus();

      // Continue tabbing to search input
      await user.tab();
      await user.tab();
      expect(screen.getByLabelText("Search Query")).toHaveFocus();
    });
  });

  describe("Error Handling", () => {
    it("handles search errors gracefully", async () => {
      const user = userEvent.setup();
      
      // Mock search error
      (searchAndTransformDiscItAPI as any).mockResolvedValue({
        success: false,
        error: "Network error occurred",
      });

      render(<EnhancedDatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

      // Perform search
      const searchButton = screen.getByRole("button", { name: /Search Discs/ });
      await user.click(searchButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText("Network error occurred")).toBeInTheDocument();
      });
    });

    it("handles loading states correctly", () => {
      render(<EnhancedDatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={true} />);

      // Search button should be disabled when loading
      const searchButton = screen.getByRole("button", { name: /Search Discs/ });
      expect(searchButton).toBeDisabled();
    });
  });
});
