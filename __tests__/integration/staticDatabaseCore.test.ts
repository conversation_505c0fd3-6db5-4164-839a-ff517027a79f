/**
 * Core Integration Test for Static Database Functionality
 *
 * Tests the complete data flow from static database API to UI integration
 * without complex UI component dependencies.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import {
  searchStaticDatabase,
  searchPDGAApprovedDiscs,
  searchCommunityFlightNumbers,
  searchManufacturerDatabase,
  getStaticDatabaseProviders,
  getStaticDatabaseManufacturers,
  getStaticDatabaseCategories,
  getPDGAStatistics,
  getCommunityStatistics,
  getManufacturerStatistics,
  clearStaticDatabaseCache,
  type StaticDatabaseProvider,
} from "@/lib/staticDatabaseAPI";

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe("Static Database Core Integration", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    clearStaticDatabaseCache();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("Multi-Provider Search Integration", () => {
    it("should handle complete search workflow for all providers", async () => {
      // Mock data for each provider
      const mockPDGAData = [
        {
          manufacturer: "Innova Champion Discs",
          model: "Destroyer",
          approvedDate: "2007-03-15",
          class: "Super Class",
        },
      ];

      const mockCommunityData = [
        {
          manufacturer: "Innova",
          mold: "Destroyer",
          speed: 12,
          glide: 5,
          turn: -1,
          fade: 3,
          stability: "Overstable",
          category: "Distance Driver",
          communityRating: 4.5,
          reviewCount: 1250,
        },
      ];

      const mockManufacturerData = [
        {
          manufacturer: "Innova",
          mold: "Destroyer",
          speed: 12,
          glide: 5,
          turn: -1,
          fade: 3,
          category: "Distance Driver",
          plasticTypes: ["Champion", "Star"],
          weightRange: { min: 165, max: 175 },
          stability: "Overstable",
          releaseYear: 2007,
        },
      ];

      // Test PDGA search
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockPDGAData),
      });

      const pdgaResult = await searchStaticDatabase({ provider: "pdga" });
      expect(pdgaResult.success).toBe(true);
      expect(pdgaResult.data).toHaveLength(1);
      expect(pdgaResult.data![0].manufacturer).toBe("Innova Champion Discs");

      // Test Community search
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockCommunityData),
      });

      const communityResult = await searchStaticDatabase({ provider: "community" });
      expect(communityResult.success).toBe(true);
      expect(communityResult.data).toHaveLength(1);
      expect(communityResult.data![0].flightNumbers.speed).toBe(12);

      // Test Manufacturer search
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockManufacturerData),
      });

      const manufacturerResult = await searchStaticDatabase({ provider: "manufacturer" });
      expect(manufacturerResult.success).toBe(true);
      expect(manufacturerResult.data).toHaveLength(1);
      expect(manufacturerResult.data![0].weight).toBe(175); // Should use max weight
    });

    it("should handle provider-specific search functions", async () => {
      const mockPDGAData = [
        {
          manufacturer: "Innova Champion Discs",
          model: "Destroyer",
          approvedDate: "2007-03-15",
          class: "Super Class",
        },
        {
          manufacturer: "Discraft",
          model: "Buzzz",
          approvedDate: "2003-08-20",
          class: "Super Class",
        },
      ];

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockPDGAData),
      });

      // Test PDGA-specific search with filters
      const pdgaResult = await searchPDGAApprovedDiscs({
        manufacturer: "Innova",
        class: "Super Class",
        approvedAfter: "2005-01-01",
      });

      expect(pdgaResult.success).toBe(true);
      expect(pdgaResult.data).toHaveLength(1);
      expect(pdgaResult.data![0].manufacturer).toBe("Innova Champion Discs");
    });

    it("should handle community-specific search with ratings", async () => {
      const mockCommunityData = [
        {
          manufacturer: "Innova",
          mold: "Destroyer",
          speed: 12,
          glide: 5,
          turn: -1,
          fade: 3,
          stability: "Overstable",
          category: "Distance Driver",
          communityRating: 4.5,
          reviewCount: 1250,
        },
        {
          manufacturer: "Discraft",
          mold: "Buzzz",
          speed: 5,
          glide: 4,
          turn: -1,
          fade: 1,
          stability: "Stable",
          category: "Midrange",
          communityRating: 4.8,
          reviewCount: 2100,
        },
      ];

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockCommunityData),
      });

      // Test community search with rating filter
      const communityResult = await searchCommunityFlightNumbers({
        minRating: 4.7,
        sortBy: "rating",
        sortOrder: "desc",
      });

      expect(communityResult.success).toBe(true);
      expect(communityResult.data).toHaveLength(1);
      expect(communityResult.data![0].mold).toBe("Buzzz");
    });

    it("should handle manufacturer-specific search with filters", async () => {
      const mockManufacturerData = [
        {
          manufacturer: "Innova",
          mold: "Destroyer",
          speed: 12,
          glide: 5,
          turn: -1,
          fade: 3,
          category: "Distance Driver",
          plasticTypes: ["Champion", "Star"],
          weightRange: { min: 165, max: 175 },
          stability: "Overstable",
          discontinued: false,
          releaseYear: 2007,
        },
        {
          manufacturer: "Innova",
          mold: "Leopard",
          speed: 6,
          glide: 5,
          turn: -2,
          fade: 1,
          category: "Fairway Driver",
          plasticTypes: ["Champion", "DX"],
          weightRange: { min: 150, max: 175 },
          stability: "Understable",
          discontinued: true,
          releaseYear: 1985,
        },
      ];

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockManufacturerData),
      });

      // Test manufacturer search with filters
      const manufacturerResult = await searchManufacturerDatabase({
        manufacturer: "Innova",
        includeDiscontinued: false,
        plasticType: "Champion",
        releaseYearRange: { min: 2000, max: 2010 },
      });

      expect(manufacturerResult.success).toBe(true);
      expect(manufacturerResult.data).toHaveLength(1);
      expect(manufacturerResult.data![0].mold).toBe("Destroyer");
    });
  });

  describe("Statistics and Analytics Integration", () => {
    it("should provide comprehensive PDGA statistics", async () => {
      const mockPDGAData = [
        {
          manufacturer: "Innova Champion Discs",
          model: "Destroyer",
          approvedDate: "2007-03-15",
          class: "Super Class",
        },
        {
          manufacturer: "Discraft",
          model: "Buzzz",
          approvedDate: "2003-08-20",
          class: "Super Class",
        },
        {
          manufacturer: "Dynamic Discs",
          model: "Judge",
          approvedDate: "2010-05-12",
          class: "Vintage Class",
        },
      ];

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockPDGAData),
      });

      const stats = await getPDGAStatistics();

      expect(stats.success).toBe(true);
      expect(stats.data!.totalDiscs).toBe(3);
      expect(stats.data!.manufacturerCount).toBe(3);
      expect(stats.data!.classBreakdown["Super Class"]).toBe(2);
      expect(stats.data!.classBreakdown["Vintage Class"]).toBe(1);
      expect(stats.data!.oldestApproval).toBe("2003-08-20");
      expect(stats.data!.newestApproval).toBe("2010-05-12");
    });

    it("should provide comprehensive community statistics", async () => {
      const mockCommunityData = [
        {
          manufacturer: "Innova",
          mold: "Destroyer",
          category: "Distance Driver",
          communityRating: 4.5,
          reviewCount: 1250,
        },
        {
          manufacturer: "Discraft",
          mold: "Buzzz",
          category: "Midrange",
          communityRating: 4.8,
          reviewCount: 2100,
        },
      ];

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockCommunityData),
      });

      const stats = await getCommunityStatistics();

      expect(stats.success).toBe(true);
      expect(stats.data!.totalDiscs).toBe(2);
      expect(stats.data!.manufacturerCount).toBe(2);
      expect(stats.data!.averageRating).toBe(4.7); // (4.5 + 4.8) / 2 = 4.65, rounded to 4.7
      expect(stats.data!.totalReviews).toBe(3350);
      expect(stats.data!.topRatedDisc!.mold).toBe("Buzzz");
      expect(stats.data!.mostReviewedDisc!.mold).toBe("Buzzz");
    });

    it("should provide comprehensive manufacturer statistics", async () => {
      const mockManufacturerData = [
        {
          manufacturer: "Innova",
          mold: "Destroyer",
          category: "Distance Driver",
          speed: 12,
          discontinued: false,
          releaseYear: 2007,
          plasticTypes: ["Champion", "Star"],
        },
        {
          manufacturer: "Innova",
          mold: "Leopard",
          category: "Fairway Driver",
          speed: 6,
          discontinued: true,
          releaseYear: 1985,
          plasticTypes: ["DX", "Pro"],
        },
        {
          manufacturer: "Discraft",
          mold: "Buzzz",
          category: "Midrange",
          speed: 5,
          discontinued: false,
          releaseYear: 2003,
          plasticTypes: ["ESP", "Z"],
        },
      ];

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockManufacturerData),
      });

      const stats = await getManufacturerStatistics();

      expect(stats.success).toBe(true);
      expect(stats.data!.totalDiscs).toBe(3);
      expect(stats.data!.manufacturerCount).toBe(2);
      expect(stats.data!.discontinuedCount).toBe(1);
      expect(stats.data!.averageSpeed).toBe(7.7); // (12 + 6 + 5) / 3 = 7.67, rounded to 7.7
      expect(stats.data!.plasticTypeCount).toBe(4); // Champion, Star, DX, Pro, ESP, Z = 6 unique
      expect(stats.data!.oldestDisc!.mold).toBe("Leopard");
      expect(stats.data!.newestDisc!.mold).toBe("Destroyer");
      expect(stats.data!.manufacturerBreakdown["Innova"]).toBe(2);
      expect(stats.data!.manufacturerBreakdown["Discraft"]).toBe(1);
    });
  });

  describe("Data Consistency and Transformation", () => {
    it("should maintain consistent data format across all providers", async () => {
      const providers: StaticDatabaseProvider[] = ["pdga", "community", "manufacturer"];
      const results = [];

      // Mock different data for each provider
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve([
            {
              manufacturer: "Innova Champion Discs",
              model: "Destroyer",
              approvedDate: "2007-03-15",
              class: "Super Class",
            },
          ]),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve([
            {
              manufacturer: "Innova",
              mold: "Destroyer",
              speed: 12,
              glide: 5,
              turn: -1,
              fade: 3,
              stability: "Overstable",
              category: "Distance Driver",
            },
          ]),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve([
            {
              manufacturer: "Innova",
              mold: "Destroyer",
              speed: 12,
              glide: 5,
              turn: -1,
              fade: 3,
              category: "Distance Driver",
              plasticTypes: ["Champion"],
              weightRange: { min: 165, max: 175 },
            },
          ]),
        });

      // Test each provider
      for (const provider of providers) {
        const result = await searchStaticDatabase({ provider });
        expect(result.success).toBe(true);
        results.push(result.data![0]);
      }

      // Verify all results have consistent Disc interface
      results.forEach((disc) => {
        expect(disc).toHaveProperty("id");
        expect(disc).toHaveProperty("manufacturer");
        expect(disc).toHaveProperty("mold");
        expect(disc).toHaveProperty("plasticType");
        expect(disc).toHaveProperty("weight");
        expect(disc).toHaveProperty("flightNumbers");
        expect(disc).toHaveProperty("condition");
        expect(disc).toHaveProperty("currentLocation");
        expect(disc).toHaveProperty("createdAt");
        expect(disc).toHaveProperty("updatedAt");

        // Verify flight numbers structure
        expect(disc.flightNumbers).toHaveProperty("speed");
        expect(disc.flightNumbers).toHaveProperty("glide");
        expect(disc.flightNumbers).toHaveProperty("turn");
        expect(disc.flightNumbers).toHaveProperty("fade");
      });
    });
  });

  describe("Provider Metadata and Utilities", () => {
    it("should provide accurate provider metadata", () => {
      const providers = getStaticDatabaseProviders();

      expect(providers).toHaveLength(3);
      expect(providers.map(p => p.provider)).toEqual(
        expect.arrayContaining(["pdga", "community", "manufacturer"])
      );

      providers.forEach((provider) => {
        expect(provider).toHaveProperty("name");
        expect(provider).toHaveProperty("description");
        expect(provider).toHaveProperty("lastUpdated");
        expect(provider).toHaveProperty("totalDiscs");
        expect(provider).toHaveProperty("version");
      });
    });

    it("should provide manufacturer and category lists for each provider", async () => {
      const mockData = [
        { manufacturer: "Innova", category: "Distance Driver" },
        { manufacturer: "Discraft", category: "Midrange" },
        { manufacturer: "Innova", category: "Putter" },
      ];

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockData),
      });

      const manufacturersResult = await getStaticDatabaseManufacturers("community");
      expect(manufacturersResult.success).toBe(true);
      expect(manufacturersResult.data).toEqual(["Discraft", "Innova"]);

      const categoriesResult = await getStaticDatabaseCategories("community");
      expect(categoriesResult.success).toBe(true);
      expect(categoriesResult.data).toEqual(["Distance Driver", "Midrange", "Putter"]);
    });
  });
});
