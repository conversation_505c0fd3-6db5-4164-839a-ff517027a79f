# Database Import Feature - DiscIt API Integration

## Overview

The Database Import feature enables users to search and import disc golf disc data from external databases, specifically
the DiscIt API. This feature extends the existing import system with a new "Database" tab in the Advanced File Uploader,
providing a seamless way to discover and import disc data from online sources.

## Features

### 🔍 **Search Interface**

- **Advanced Filtering**: Search by brand, category, speed, and stability
- **Real-time Results**: Live search with loading states and error handling
- **Responsive Design**: Works across desktop and mobile devices

### 📊 **Data Integration**

- **Seamless Import**: Integrates with existing import pipeline
- **Data Transformation**: Converts DiscIt API format to internal Disc format
- **Validation**: Comprehensive data validation and error handling

### 🎯 **User Experience**

- **Selective Import**: Choose specific discs or select all results
- **Progress Tracking**: Clear feedback during search and import operations
- **Error Recovery**: Graceful error handling with user-friendly messages

## Technical Implementation

### Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    AdvancedFileUploader                     │
├─────────────────────────────────────────────────────────────┤
│ Files │ URL │ Clipboard │ Text │ Database ← New Tab         │
└─────────────────────────────────────────────────────────────┘
                                      │
                                      ▼
┌─────────────────────────────────────────────────────────────┐
│                  DatabaseSearchTab                          │
├─────────────────────────────────────────────────────────────┤
│ • Search Filters (Brand, Category, Speed, Stability)       │
│ • Results Display with Selection                           │
│ • Import Button for Selected Discs                         │
└─────────────────────────────────────────────────────────────┘
                                      │
                                      ▼
┌─────────────────────────────────────────────────────────────┐
│                   DiscIt API Service                        │
├─────────────────────────────────────────────────────────────┤
│ • HTTP Client with Timeout & Retry Logic                   │
│ • Query Building & Parameter Validation                    │
│ • Error Handling & Response Processing                     │
└─────────────────────────────────────────────────────────────┘
                                      │
                                      ▼
┌─────────────────────────────────────────────────────────────┐
│                 Data Transformation                         │
├─────────────────────────────────────────────────────────────┤
│ • DiscIt Format → Internal Disc Format                     │
│ • Default Value Assignment                                  │
│ • Validation & Error Handling                              │
└─────────────────────────────────────────────────────────────┘
                                      │
                                      ▼
┌─────────────────────────────────────────────────────────────┐
│                  Existing Import Pipeline                   │
├─────────────────────────────────────────────────────────────┤
│ • JSON Processing                                           │
│ • Validation & Preview                                      │
│ • Merge Options & Final Import                             │
└─────────────────────────────────────────────────────────────┘
```

### Key Components

#### 1. **Type System Extensions**

```typescript
// Extended ImportSourceType to include database
export type ImportSourceType = "file" | "url" | "clipboard" | "text" | "database";

// New database-specific types
export interface DatabaseImportSource {
  type: "database";
  provider: "discit-api";
  searchQuery?: string;
  filters?: DatabaseImportFilters;
}

export interface DatabaseImportFilters {
  brand?: string;
  category?: string;
  speed?: number;
  stability?: string;
}
```

#### 2. **API Service Layer**

```typescript
// Main search function with comprehensive error handling
export async function searchAndTransformDiscItAPI(filters: DiscItSearchFilters = {}): Promise<APIServiceResult<Disc[]>>;

// Utility functions for filter options
export async function getDiscItBrands(): Promise<APIServiceResult<string[]>>;
export async function getDiscItCategories(): Promise<APIServiceResult<string[]>>;
export async function getDiscItStabilityOptions(): Promise<APIServiceResult<string[]>>;
```

#### 3. **Data Transformation**

```typescript
// Transform DiscIt API format to internal Disc format
export function transformDiscItData(apiDiscs: DiscItDisc[]): Disc[];

// Safe transformation with error handling
export function transformDiscItDataSafe(apiDiscs: DiscItDisc[]): APIServiceResult<Disc[]>;
```

## Usage Guide

### For Users

1. **Navigate to Import**: Go to `/import/advanced`
2. **Select Database Tab**: Click the "Database" tab (5th tab)
3. **Set Filters**: Choose brand, category, speed, or stability filters
4. **Search**: Click "Search Discs" to query the DiscIt API
5. **Select Discs**: Choose individual discs or "Select All"
6. **Import**: Click "Import Selected" to add discs to your collection

### For Developers

#### Adding New Database Providers

1. **Extend Provider Type**:

```typescript
export interface DatabaseImportSource {
  type: "database";
  provider: "discit-api" | "new-provider"; // Add new provider
  // ... rest of interface
}
```

2. **Create Provider Service**:

```typescript
// Create new service file: lib/newProviderAPI.ts
export async function searchNewProviderAPI(filters: NewProviderFilters): Promise<APIServiceResult<Disc[]>>;
```

3. **Update DatabaseSearchTab**:

```typescript
// Add provider selection logic
const handleProviderChange = (provider: string) => {
  // Switch between different API services
};
```

#### Extending Filter Options

1. **Add to DatabaseImportFilters**:

```typescript
export interface DatabaseImportFilters {
  // ... existing filters
  newFilter?: string;
}
```

2. **Update UI Components**:

```typescript
// Add new filter dropdown in DatabaseSearchTab
<select onChange={(e) => handleFilterChange("newFilter", e.target.value)}>{/* Filter options */}</select>
```

## API Reference

### DiscIt API Integration

**Base URL**: `https://discit-api.fly.dev`

**Endpoint**: `/disc`

**Parameters**:

- `brand` (string): Filter by manufacturer
- `category` (string): Filter by disc category
- `speed` (number): Filter by speed rating
- `stability` (string): Filter by stability rating
- `limit` (number): Maximum results (default: 50, max: 100)
- `offset` (number): Pagination offset

**Response Format**:

```typescript
interface DiscItDisc {
  id: string;
  name: string;
  brand: string;
  category: string;
  speed: number;
  glide: number;
  turn: number;
  fade: number;
  stability: string;
  link: string;
  pic: string;
  // ... additional fields
}
```

## Configuration

### API Settings

```typescript
export const DISCIT_API_CONFIG = {
  baseURL: "https://discit-api.fly.dev",
  endpoints: {
    search: "/disc",
  },
  timeout: 10000, // 10 seconds
  maxRetries: 3,
  defaultLimit: 50,
  maxLimit: 100,
} as const;
```

### Default Values

When transforming DiscIt data to internal format:

- **Weight**: 175g (standard disc weight)
- **Condition**: NEW
- **Location**: HOME
- **Plastic Type**: "Unknown" (DiscIt API doesn't provide this)

## Error Handling

### Network Errors

- **Timeout**: 10-second timeout with user-friendly message
- **Retry Logic**: Up to 3 retries with exponential backoff
- **Connection Issues**: Clear error messages for network problems

### API Errors

- **4xx Errors**: No retry, immediate user feedback
- **5xx Errors**: Retry with backoff, then user notification
- **Rate Limiting**: Handled gracefully with appropriate delays

### Data Validation

- **Invalid Responses**: Comprehensive validation of API responses
- **Missing Fields**: Default values assigned for missing data
- **Type Safety**: Full TypeScript validation throughout

## Testing

### Unit Tests

- **API Service**: Mock HTTP requests and responses
- **Data Transformation**: Test format conversion and validation
- **Type System**: Verify type safety and interface compliance

### Integration Tests

- **Component Testing**: Full DatabaseSearchTab functionality
- **End-to-End Flow**: Search → Select → Import workflow
- **Error Scenarios**: Network failures and invalid responses

### Manual Testing

- **Real API Integration**: Live testing with DiscIt API
- **UI Responsiveness**: Cross-device compatibility
- **Performance**: Search speed and memory usage

## Performance Considerations

### Optimization Strategies

- **Request Debouncing**: Prevent excessive API calls
- **Result Caching**: Cache filter options and recent searches
- **Pagination**: Limit results to prevent UI overload
- **Memory Management**: Efficient data structures and cleanup

### Monitoring

- **API Response Times**: Track search performance
- **Error Rates**: Monitor API reliability
- **User Engagement**: Track feature usage and success rates

## Future Enhancements

### Planned Features

1. **Multiple Providers**: Support for additional disc databases
2. **Advanced Search**: Full-text search across disc descriptions
3. **Favorites**: Save frequently searched discs
4. **Bulk Operations**: Import entire manufacturer catalogs
5. **Offline Mode**: Cache popular discs for offline access

### Technical Improvements

1. **GraphQL Integration**: More efficient data fetching
2. **Real-time Updates**: WebSocket connections for live data
3. **Advanced Caching**: Redis-based caching layer
4. **Analytics**: Detailed usage analytics and insights

## Troubleshooting

### Common Issues

**Search Returns No Results**:

- Check filter combinations (too restrictive)
- Verify API connectivity
- Try broader search criteria

**Import Fails**:

- Check network connection
- Verify disc data format
- Review browser console for errors

**Performance Issues**:

- Reduce search result limit
- Clear browser cache
- Check network speed

### Debug Mode

Enable debug logging:

```typescript
// In browser console
localStorage.setItem("debug", "discit-api");
```

## Support

For technical support or feature requests:

- **Documentation**: Check this guide and API documentation
- **Issues**: Report bugs through the project issue tracker
- **Community**: Join the disc golf inventory community forums
