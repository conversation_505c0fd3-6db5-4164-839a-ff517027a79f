# Database Import Examples

This document provides practical examples of using the Database Import feature with the DiscIt API integration.

## Basic Usage Examples

### Example 1: Search by Brand

```typescript
// Search for all Innova discs
const filters: DiscItSearchFilters = {
  brand: "Innova",
  limit: 25
};

const result = await searchAndTransformDiscItAPI(filters);
if (result.success) {
  console.log(`Found ${result.data?.length} Innova discs`);
}
```

### Example 2: Search by Category and Speed

```typescript
// Search for distance drivers with speed 12
const filters: DiscItSearchFilters = {
  category: "Distance Driver",
  speed: 12,
  limit: 50
};

const result = await searchAndTransformDiscItAPI(filters);
```

### Example 3: Search by Stability

```typescript
// Search for overstable discs
const filters: DiscItSearchFilters = {
  stability: "Overstable",
  limit: 30
};

const result = await searchAndTransformDiscItAPI(filters);
```

## Advanced Usage Examples

### Example 4: Complex Filter Combination

```typescript
// Search for Innova overstable distance drivers with speed 11-12
const searchInnovaDrivers = async () => {
  const filters: DiscItSearchFilters = {
    brand: "Innova",
    category: "Distance Driver",
    stability: "Overstable",
    limit: 20
  };

  try {
    const result = await searchAndTransformDiscItAPI(filters);
    
    if (result.success && result.data) {
      // Filter by speed range (API doesn't support range queries)
      const speedFiltered = result.data.filter(disc => 
        disc.flightNumbers.speed >= 11 && disc.flightNumbers.speed <= 12
      );
      
      console.log(`Found ${speedFiltered.length} matching discs`);
      return speedFiltered;
    }
  } catch (error) {
    console.error("Search failed:", error);
  }
};
```

### Example 5: Batch Import with Error Handling

```typescript
// Import multiple disc types with comprehensive error handling
const batchImportDiscs = async () => {
  const searchQueries = [
    { brand: "Innova", category: "Putter" },
    { brand: "Discraft", category: "Midrange" },
    { brand: "Dynamic Discs", category: "Fairway Driver" }
  ];

  const allDiscs: Disc[] = [];
  const errors: string[] = [];

  for (const query of searchQueries) {
    try {
      const result = await searchAndTransformDiscItAPI(query);
      
      if (result.success && result.data) {
        allDiscs.push(...result.data);
        console.log(`✅ Imported ${result.data.length} ${query.category}s from ${query.brand}`);
      } else {
        errors.push(`❌ Failed to import ${query.category}s from ${query.brand}: ${result.error}`);
      }
      
      // Rate limiting: wait between requests
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      errors.push(`❌ Network error for ${query.brand} ${query.category}: ${error}`);
    }
  }

  console.log(`\n📊 Import Summary:`);
  console.log(`Total discs imported: ${allDiscs.length}`);
  console.log(`Errors encountered: ${errors.length}`);
  
  if (errors.length > 0) {
    console.log(`\n🚨 Errors:`);
    errors.forEach(error => console.log(error));
  }

  return { discs: allDiscs, errors };
};
```

## Component Usage Examples

### Example 6: Custom DatabaseSearchTab Implementation

```tsx
// Custom implementation with additional features
import React, { useState, useEffect } from 'react';
import { DatabaseSearchTab } from '@/components/import/DatabaseSearchTab';
import type { Disc } from '@/lib/types';

const CustomDatabaseImport: React.FC = () => {
  const [selectedDiscs, setSelectedDiscs] = useState<Disc[]>([]);
  const [importHistory, setImportHistory] = useState<Disc[][]>([]);

  const handleDiscSelect = (discs: Disc[]) => {
    setSelectedDiscs(discs);
    
    // Add to import history
    setImportHistory(prev => [...prev, discs]);
    
    // Process the import
    console.log(`Importing ${discs.length} discs:`, discs);
    
    // You could integrate with your existing import pipeline here
    // importDiscsToCollection(discs);
  };

  const handleClearHistory = () => {
    setImportHistory([]);
  };

  return (
    <div className="space-y-6">
      <DatabaseSearchTab 
        onDiscSelect={handleDiscSelect}
        isLoading={false}
      />
      
      {/* Import History */}
      {importHistory.length > 0 && (
        <div className="border rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Import History</h3>
            <button 
              onClick={handleClearHistory}
              className="text-sm text-red-600 hover:text-red-800"
            >
              Clear History
            </button>
          </div>
          
          {importHistory.map((batch, index) => (
            <div key={index} className="mb-2 p-2 bg-gray-50 rounded">
              <span className="text-sm text-gray-600">
                Batch {index + 1}: {batch.length} discs imported
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
```

### Example 7: Integration with Existing Import Pipeline

```tsx
// Integration with the existing AdvancedFileUploader
import React from 'react';
import { AdvancedFileUploader } from '@/components/import/AdvancedFileUploader';
import { createDatabaseImportSource } from '@/lib/advancedImportSources';
import type { ImportSource } from '@/lib/advancedImportSources';

const ImportPage: React.FC = () => {
  const handleSourceSelect = (source: ImportSource) => {
    console.log('Import source selected:', source);
    
    // The source will be processed by the existing import pipeline
    if (source.type === 'database') {
      console.log('Database import detected');
      console.log('Search config:', source.searchConfig);
      console.log('Selected discs data:', source.data);
    }
    
    // Continue with existing import flow...
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Import Discs</h1>
      
      <AdvancedFileUploader onSourceSelect={handleSourceSelect} />
    </div>
  );
};
```

## API Service Examples

### Example 8: Custom API Service with Caching

```typescript
// Enhanced API service with caching
class CachedDiscItService {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  private getCacheKey(filters: DiscItSearchFilters): string {
    return JSON.stringify(filters);
  }

  private isValidCache(timestamp: number): boolean {
    return Date.now() - timestamp < this.cacheTimeout;
  }

  async searchWithCache(filters: DiscItSearchFilters): Promise<APIServiceResult<Disc[]>> {
    const cacheKey = this.getCacheKey(filters);
    const cached = this.cache.get(cacheKey);

    // Return cached result if valid
    if (cached && this.isValidCache(cached.timestamp)) {
      console.log('🎯 Cache hit for filters:', filters);
      return {
        success: true,
        data: cached.data,
        total: cached.data.length
      };
    }

    // Fetch fresh data
    console.log('🌐 Fetching fresh data for filters:', filters);
    const result = await searchAndTransformDiscItAPI(filters);

    // Cache successful results
    if (result.success && result.data) {
      this.cache.set(cacheKey, {
        data: result.data,
        timestamp: Date.now()
      });
    }

    return result;
  }

  clearCache(): void {
    this.cache.clear();
    console.log('🗑️ Cache cleared');
  }
}

// Usage
const cachedService = new CachedDiscItService();

const searchWithCaching = async () => {
  const filters = { brand: "Innova", category: "Putter" };
  
  // First call - fetches from API
  const result1 = await cachedService.searchWithCache(filters);
  
  // Second call - returns cached result
  const result2 = await cachedService.searchWithCache(filters);
};
```

### Example 9: Retry Logic with Exponential Backoff

```typescript
// Custom retry implementation
const searchWithCustomRetry = async (
  filters: DiscItSearchFilters,
  maxRetries: number = 3
): Promise<APIServiceResult<Disc[]>> => {
  let lastError: string = '';

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 Attempt ${attempt + 1}/${maxRetries + 1}`);
      
      const result = await searchDiscItAPI(filters);
      
      if (result.success) {
        console.log('✅ Search successful');
        return transformDiscItDataSafe(result.data || []);
      }

      lastError = result.error || 'Unknown error';
      
      // Don't retry on client errors
      if (lastError.includes('400') || lastError.includes('404')) {
        break;
      }

      // Wait before retry (exponential backoff)
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000;
        console.log(`⏳ Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }

    } catch (error) {
      lastError = error instanceof Error ? error.message : 'Network error';
      console.error(`❌ Attempt ${attempt + 1} failed:`, lastError);
    }
  }

  return {
    success: false,
    error: `Failed after ${maxRetries + 1} attempts: ${lastError}`
  };
};
```

## Testing Examples

### Example 10: Unit Test for API Service

```typescript
// Unit test example
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { searchDiscItAPI, transformDiscItData } from '@/lib/discDatabaseAPI';

describe('DiscIt API Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should search and return transformed disc data', async () => {
    // Mock API response
    const mockApiResponse = [{
      id: '1',
      name: 'Destroyer',
      brand: 'Innova',
      category: 'Distance Driver',
      speed: 12,
      glide: 5,
      turn: -1,
      fade: 3,
      stability: 'Overstable',
      link: 'https://example.com',
      pic: 'https://example.com/pic.jpg',
      nameSlug: 'destroyer',
      brandSlug: 'innova',
      categorySlug: 'distance-driver',
      stabilitySlug: 'overstable',
      color: 'Red',
      backgroundColorHex: '#ff0000',
      textColorHex: '#ffffff',
    }];

    // Mock fetch
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: async () => mockApiResponse,
    });

    // Test search
    const result = await searchDiscItAPI({ brand: 'Innova' });
    
    expect(result.success).toBe(true);
    expect(result.data).toHaveLength(1);
    expect(result.data![0].name).toBe('Destroyer');

    // Test transformation
    const transformed = transformDiscItData(mockApiResponse);
    
    expect(transformed).toHaveLength(1);
    expect(transformed[0].manufacturer).toBe('Innova');
    expect(transformed[0].mold).toBe('Destroyer');
    expect(transformed[0].flightNumbers.speed).toBe(12);
  });
});
```

### Example 11: Integration Test

```typescript
// Integration test example
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DatabaseSearchTab } from '@/components/import/DatabaseSearchTab';

describe('DatabaseSearchTab Integration', () => {
  it('should perform search and display results', async () => {
    const mockOnDiscSelect = vi.fn();
    
    render(<DatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

    // Set filters
    const brandSelect = screen.getByLabelText('Brand');
    fireEvent.change(brandSelect, { target: { value: 'Innova' } });

    // Perform search
    const searchButton = screen.getByRole('button', { name: /search discs/i });
    fireEvent.click(searchButton);

    // Wait for results
    await waitFor(() => {
      expect(screen.getByText('Search Results')).toBeInTheDocument();
    });

    // Verify results are displayed
    expect(screen.getByText(/found \d+ discs/i)).toBeInTheDocument();
  });
});
```

## Best Practices

### Performance Optimization
1. **Debounce Search**: Prevent excessive API calls during typing
2. **Limit Results**: Use reasonable limits (50-100 discs max)
3. **Cache Results**: Cache filter options and recent searches
4. **Lazy Loading**: Load additional results on demand

### Error Handling
1. **User-Friendly Messages**: Convert technical errors to user-friendly text
2. **Retry Logic**: Implement exponential backoff for transient failures
3. **Fallback Options**: Provide alternative actions when searches fail
4. **Logging**: Log errors for debugging while protecting user privacy

### Data Management
1. **Validation**: Always validate API responses before processing
2. **Default Values**: Provide sensible defaults for missing data
3. **Type Safety**: Use TypeScript throughout for compile-time safety
4. **Sanitization**: Sanitize user inputs before sending to API

### User Experience
1. **Loading States**: Show clear loading indicators during searches
2. **Progress Feedback**: Indicate search progress and results count
3. **Selection Management**: Make disc selection intuitive and clear
4. **Responsive Design**: Ensure functionality across all device sizes
