/**
 * Import History & Management for Disc Golf Inventory Management System
 *
 * This module provides utilities for tracking import operations,
 * managing import history, and implementing undo/redo functionality.
 */

import type { Disc } from "./types";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Import operation types
 */
export type ImportOperationType = "import" | "batch-import" | "url-import" | "clipboard-import" | "text-import";

/**
 * Import operation status
 */
export type ImportOperationStatus = "pending" | "in-progress" | "completed" | "failed" | "cancelled";

/**
 * Import operation record
 */
export interface ImportOperation {
  id: string;
  type: ImportOperationType;
  status: ImportOperationStatus;
  timestamp: Date;
  source: {
    type: "file" | "url" | "clipboard" | "text" | "batch";
    name: string;
    size?: number;
  };
  result: {
    totalDiscs: number;
    newDiscs: number;
    duplicates: number;
    errors: number;
    mergeStrategy: "replace" | "merge" | "append";
  };
  metadata: {
    duration: number; // in milliseconds
    fileFormat?: "json" | "csv";
    templateUsed?: string;
    userAgent?: string;
  };
  snapshot?: {
    beforeCount: number;
    afterCount: number;
    beforeData?: Disc[]; // For undo functionality
  };
  error?: string;
}

/**
 * Import history summary
 */
export interface ImportHistorySummary {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  totalDiscsImported: number;
  lastImportDate?: Date;
  mostUsedSource: string;
  averageDuration: number;
}

/**
 * Undo/Redo state
 */
export interface UndoRedoState {
  canUndo: boolean;
  canRedo: boolean;
  undoOperation?: ImportOperation;
  redoOperation?: ImportOperation;
}

// ============================================================================
// STORAGE UTILITIES
// ============================================================================

const HISTORY_STORAGE_KEY = "disc-golf-import-history";
const UNDO_STORAGE_KEY = "disc-golf-import-undo";
const MAX_HISTORY_ENTRIES = 100;
const MAX_UNDO_ENTRIES = 10;

/**
 * Load import history from localStorage
 */
function loadHistoryFromStorage(): ImportOperation[] {
  try {
    const stored = localStorage.getItem(HISTORY_STORAGE_KEY);
    if (!stored) return [];

    const data = JSON.parse(stored);
    return (
      data.operations?.map((op: any) => ({
        ...op,
        timestamp: new Date(op.timestamp),
      })) || []
    );
  } catch (error) {
    console.error("Failed to load import history:", error);
    return [];
  }
}

/**
 * Save import history to localStorage
 */
function saveHistoryToStorage(operations: ImportOperation[]): void {
  try {
    // Keep only the most recent entries
    const trimmedOperations = operations.slice(-MAX_HISTORY_ENTRIES);

    const data = {
      version: "1.0",
      operations: trimmedOperations,
      lastUpdated: new Date().toISOString(),
    };

    localStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error("Failed to save import history:", error);
  }
}

/**
 * Load undo stack from localStorage
 */
function loadUndoStackFromStorage(): ImportOperation[] {
  try {
    const stored = localStorage.getItem(UNDO_STORAGE_KEY);
    if (!stored) return [];

    const data = JSON.parse(stored);
    return (
      data.undoStack?.map((op: any) => ({
        ...op,
        timestamp: new Date(op.timestamp),
      })) || []
    );
  } catch (error) {
    console.error("Failed to load undo stack:", error);
    return [];
  }
}

/**
 * Save undo stack to localStorage
 */
function saveUndoStackToStorage(undoStack: ImportOperation[]): void {
  try {
    // Keep only the most recent entries
    const trimmedStack = undoStack.slice(-MAX_UNDO_ENTRIES);

    const data = {
      version: "1.0",
      undoStack: trimmedStack,
      lastUpdated: new Date().toISOString(),
    };

    localStorage.setItem(UNDO_STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error("Failed to save undo stack:", error);
  }
}

/**
 * Generate unique operation ID
 */
function generateOperationId(): string {
  return `import_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// ============================================================================
// HISTORY MANAGEMENT
// ============================================================================

/**
 * Create a new import operation record
 */
export function createImportOperation(
  type: ImportOperationType,
  source: ImportOperation["source"],
  beforeData?: Disc[]
): ImportOperation {
  return {
    id: generateOperationId(),
    type,
    status: "pending",
    timestamp: new Date(),
    source,
    result: {
      totalDiscs: 0,
      newDiscs: 0,
      duplicates: 0,
      errors: 0,
      mergeStrategy: "merge",
    },
    metadata: {
      duration: 0,
      userAgent: typeof navigator !== "undefined" ? navigator.userAgent : undefined,
    },
    snapshot: beforeData
      ? {
          beforeCount: beforeData.length,
          afterCount: 0,
          beforeData,
        }
      : undefined,
  };
}

/**
 * Update import operation with results
 */
export function updateImportOperation(operation: ImportOperation, updates: Partial<ImportOperation>): ImportOperation {
  return {
    ...operation,
    ...updates,
    metadata: {
      ...operation.metadata,
      ...updates.metadata,
    },
    result: {
      ...operation.result,
      ...updates.result,
    },
    snapshot: {
      ...operation.snapshot,
      ...updates.snapshot,
    },
  };
}

/**
 * Record import operation in history
 */
export function recordImportOperation(operation: ImportOperation): void {
  const history = loadHistoryFromStorage();
  history.push(operation);
  saveHistoryToStorage(history);

  // Add to undo stack if successful and has snapshot
  if (operation.status === "completed" && operation.snapshot?.beforeData) {
    const undoStack = loadUndoStackFromStorage();
    undoStack.push(operation);
    saveUndoStackToStorage(undoStack);
  }
}

/**
 * Get import history
 */
export function getImportHistory(): ImportOperation[] {
  return loadHistoryFromStorage();
}

/**
 * Get import history summary
 */
export function getImportHistorySummary(): ImportHistorySummary {
  const history = getImportHistory();

  if (history.length === 0) {
    return {
      totalOperations: 0,
      successfulOperations: 0,
      failedOperations: 0,
      totalDiscsImported: 0,
      mostUsedSource: "none",
      averageDuration: 0,
    };
  }

  const successful = history.filter((op) => op.status === "completed");
  const failed = history.filter((op) => op.status === "failed");

  const totalDiscsImported = successful.reduce((sum, op) => sum + op.result.newDiscs, 0);
  const averageDuration =
    successful.length > 0 ? successful.reduce((sum, op) => sum + op.metadata.duration, 0) / successful.length : 0;

  // Find most used source
  const sourceCounts = history.reduce((counts, op) => {
    counts[op.source.type] = (counts[op.source.type] || 0) + 1;
    return counts;
  }, {} as Record<string, number>);

  const mostUsedSource = Object.entries(sourceCounts).sort(([, a], [, b]) => b - a)[0]?.[0] || "none";

  return {
    totalOperations: history.length,
    successfulOperations: successful.length,
    failedOperations: failed.length,
    totalDiscsImported,
    lastImportDate: history[history.length - 1]?.timestamp,
    mostUsedSource,
    averageDuration,
  };
}

/**
 * Clear import history
 */
export function clearImportHistory(): void {
  localStorage.removeItem(HISTORY_STORAGE_KEY);
  localStorage.removeItem(UNDO_STORAGE_KEY);
}

/**
 * Delete specific import operation
 */
export function deleteImportOperation(operationId: string): boolean {
  const history = loadHistoryFromStorage();
  const index = history.findIndex((op) => op.id === operationId);

  if (index === -1) return false;

  history.splice(index, 1);
  saveHistoryToStorage(history);
  return true;
}

// ============================================================================
// UNDO/REDO FUNCTIONALITY
// ============================================================================

/**
 * Get undo/redo state
 */
export function getUndoRedoState(): UndoRedoState {
  const undoStack = loadUndoStackFromStorage();

  return {
    canUndo: undoStack.length > 0,
    canRedo: false, // Simplified - no redo stack for now
    undoOperation: undoStack[undoStack.length - 1],
  };
}

/**
 * Undo last import operation
 */
export function undoLastImport(): { success: boolean; operation?: ImportOperation; error?: string } {
  const undoStack = loadUndoStackFromStorage();

  if (undoStack.length === 0) {
    return { success: false, error: "No operations to undo" };
  }

  const lastOperation = undoStack.pop()!;

  if (!lastOperation.snapshot?.beforeData) {
    return { success: false, error: "Cannot undo: no snapshot data available" };
  }

  // Save updated undo stack
  saveUndoStackToStorage(undoStack);

  return { success: true, operation: lastOperation };
}

/**
 * Check if operation can be undone
 */
export function canUndoOperation(operation: ImportOperation): boolean {
  return (
    operation.status === "completed" &&
    operation.snapshot?.beforeData !== undefined &&
    operation.result.mergeStrategy !== "append"
  ); // Append operations are harder to undo
}

// ============================================================================
// SEARCH AND FILTERING
// ============================================================================

/**
 * Search import history
 */
export function searchImportHistory(query: string): ImportOperation[] {
  const history = getImportHistory();
  const lowerQuery = query.toLowerCase();

  return history.filter(
    (operation) =>
      operation.source.name.toLowerCase().includes(lowerQuery) ||
      operation.type.toLowerCase().includes(lowerQuery) ||
      operation.status.toLowerCase().includes(lowerQuery) ||
      operation.error?.toLowerCase().includes(lowerQuery)
  );
}

/**
 * Filter import history by criteria
 */
export function filterImportHistory(criteria: {
  type?: ImportOperationType;
  status?: ImportOperationStatus;
  dateFrom?: Date;
  dateTo?: Date;
  sourceType?: string;
}): ImportOperation[] {
  const history = getImportHistory();

  return history.filter((operation) => {
    if (criteria.type && operation.type !== criteria.type) return false;
    if (criteria.status && operation.status !== criteria.status) return false;
    if (criteria.sourceType && operation.source.type !== criteria.sourceType) return false;

    if (criteria.dateFrom && operation.timestamp < criteria.dateFrom) return false;
    if (criteria.dateTo && operation.timestamp > criteria.dateTo) return false;

    return true;
  });
}

/**
 * Get import statistics by time period
 */
export function getImportStatsByPeriod(days: number): {
  period: string;
  operations: number;
  discsImported: number;
  successRate: number;
} {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);

  const recentOperations = filterImportHistory({
    dateFrom: cutoffDate,
  });

  const successful = recentOperations.filter((op) => op.status === "completed");
  const totalDiscs = successful.reduce((sum, op) => sum + op.result.newDiscs, 0);
  const successRate = recentOperations.length > 0 ? (successful.length / recentOperations.length) * 100 : 0;

  return {
    period: `Last ${days} days`,
    operations: recentOperations.length,
    discsImported: totalDiscs,
    successRate: Math.round(successRate),
  };
}
