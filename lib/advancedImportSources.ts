/**
 * Advanced Import Sources for Disc Golf Inventory Management System
 *
 * This module provides utilities for importing from various sources including
 * URLs, clipboard data, and enhanced file handling.
 */

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Import source types
 */
export type ImportSourceType = "file" | "url" | "clipboard" | "text" | "database";

/**
 * Base import source data
 */
export interface BaseImportSource {
  type: ImportSourceType;
  name: string;
  size?: number;
  format?: "json" | "csv" | "unknown";
}

/**
 * File/URL/Clipboard/Text import source
 */
export interface DataImportSource extends BaseImportSource {
  type: "file" | "url" | "clipboard" | "text";
  data: string | File;
}

/**
 * Database import source
 */
export interface DatabaseImportSourceData extends BaseImportSource {
  type: "database";
  data: string; // JSON string of selected discs
  searchConfig: DatabaseImportSource;
}

/**
 * Union type for all import sources
 */
export type ImportSource = DataImportSource | DatabaseImportSourceData;

/**
 * URL import options
 */
export interface URLImportOptions {
  timeout?: number;
  maxSize?: number;
  allowedDomains?: string[];
  headers?: Record<string, string>;
}

/**
 * Import source result
 */
export interface ImportSourceResult {
  success: boolean;
  source?: ImportSource;
  error?: string;
}

/**
 * Database import source filters
 */
export interface DatabaseImportFilters {
  brand?: string;
  category?: string;
  speed?: number;
  stability?: string;
}

/**
 * Database import source configuration
 */
export interface DatabaseImportSource {
  type: "database";
  provider: "discit-api";
  searchQuery?: string;
  filters?: DatabaseImportFilters;
}

// ============================================================================
// URL IMPORT UTILITIES
// ============================================================================

/**
 * Default URL import options
 */
const DEFAULT_URL_OPTIONS: URLImportOptions = {
  timeout: 30000, // 30 seconds
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedDomains: [], // Empty means all domains allowed
};

/**
 * Validate URL for import
 */
function validateURL(url: string, options: URLImportOptions): { isValid: boolean; error?: string } {
  try {
    const urlObj = new URL(url);

    // Check protocol
    if (!["http:", "https:"].includes(urlObj.protocol)) {
      return { isValid: false, error: "Only HTTP and HTTPS URLs are supported" };
    }

    // Check allowed domains
    if (options.allowedDomains && options.allowedDomains.length > 0) {
      const isAllowed = options.allowedDomains.some(
        (domain) => urlObj.hostname === domain || urlObj.hostname.endsWith(`.${domain}`)
      );
      if (!isAllowed) {
        return { isValid: false, error: "Domain not allowed for import" };
      }
    }

    return { isValid: true };
  } catch {
    return { isValid: false, error: "Invalid URL format" };
  }
}

/**
 * Detect file format from URL or content
 */
function detectFormatFromURL(url: string, contentType?: string): "json" | "csv" | "unknown" {
  const urlLower = url.toLowerCase();

  if (urlLower.includes(".json") || contentType?.includes("application/json")) {
    return "json";
  }

  if (urlLower.includes(".csv") || contentType?.includes("text/csv") || contentType?.includes("application/csv")) {
    return "csv";
  }

  return "unknown";
}

/**
 * Import data from URL
 */
export async function importFromURL(url: string, options: URLImportOptions = {}): Promise<ImportSourceResult> {
  const mergedOptions = { ...DEFAULT_URL_OPTIONS, ...options };

  // Validate URL
  const validation = validateURL(url, mergedOptions);
  if (!validation.isValid) {
    return { success: false, error: validation.error };
  }

  try {
    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), mergedOptions.timeout);

    // Fetch data
    const response = await fetch(url, {
      signal: controller.signal,
      headers: {
        Accept: "application/json, text/csv, text/plain",
        ...mergedOptions.headers,
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      return {
        success: false,
        error: `Failed to fetch data: ${response.status} ${response.statusText}`,
      };
    }

    // Check content length
    const contentLength = response.headers.get("content-length");
    if (contentLength && parseInt(contentLength) > mergedOptions.maxSize!) {
      return {
        success: false,
        error: `File too large: ${(parseInt(contentLength) / 1024 / 1024).toFixed(1)}MB (max: ${(
          mergedOptions.maxSize! /
          1024 /
          1024
        ).toFixed(1)}MB)`,
      };
    }

    // Get content
    const content = await response.text();

    // Check actual size
    if (content.length > mergedOptions.maxSize!) {
      return {
        success: false,
        error: `Content too large: ${(content.length / 1024 / 1024).toFixed(1)}MB`,
      };
    }

    // Detect format
    const contentType = response.headers.get("content-type");
    const format = detectFormatFromURL(url, contentType || undefined);

    // Create source
    const source: ImportSource = {
      type: "url",
      name: url.split("/").pop() || url,
      data: content,
      size: content.length,
      format,
    };

    return { success: true, source };
  } catch (error) {
    if (error instanceof Error && error.name === "AbortError") {
      return { success: false, error: "Request timed out" };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch data from URL",
    };
  }
}

// ============================================================================
// CLIPBOARD IMPORT UTILITIES
// ============================================================================

/**
 * Check if clipboard API is available
 */
export function isClipboardAvailable(): boolean {
  return typeof navigator !== "undefined" && "clipboard" in navigator && "readText" in navigator.clipboard;
}

/**
 * Detect format from clipboard content
 */
function detectFormatFromContent(content: string): "json" | "csv" | "unknown" {
  const trimmed = content.trim();

  // Try to parse as JSON
  try {
    JSON.parse(trimmed);
    return "json";
  } catch {
    // Not JSON
  }

  // Check for CSV patterns
  const lines = trimmed.split("\n");
  if (lines.length > 1) {
    const firstLine = lines[0];
    const hasCommas = firstLine.includes(",");
    const hasConsistentColumns = lines
      .slice(1, 5)
      .every((line) => line.split(",").length === firstLine.split(",").length);

    if (hasCommas && hasConsistentColumns) {
      return "csv";
    }
  }

  return "unknown";
}

/**
 * Import data from clipboard
 */
export async function importFromClipboard(): Promise<ImportSourceResult> {
  if (!isClipboardAvailable()) {
    return {
      success: false,
      error: "Clipboard API not available in this browser",
    };
  }

  try {
    const content = await navigator.clipboard.readText();

    if (!content.trim()) {
      return {
        success: false,
        error: "Clipboard is empty",
      };
    }

    // Detect format
    const format = detectFormatFromContent(content);

    if (format === "unknown") {
      return {
        success: false,
        error: "Clipboard content is not in a recognized format (JSON or CSV)",
      };
    }

    // Create source
    const source: ImportSource = {
      type: "clipboard",
      name: `Clipboard Data (${format.toUpperCase()})`,
      data: content,
      size: content.length,
      format,
    };

    return { success: true, source };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to read clipboard",
    };
  }
}

// ============================================================================
// TEXT IMPORT UTILITIES
// ============================================================================

/**
 * Import data from raw text
 */
export function importFromText(text: string, name?: string): ImportSourceResult {
  if (!text.trim()) {
    return {
      success: false,
      error: "Text content is empty",
    };
  }

  // Detect format
  const format = detectFormatFromContent(text);

  if (format === "unknown") {
    return {
      success: false,
      error: "Text content is not in a recognized format (JSON or CSV)",
    };
  }

  // Create source
  const source: ImportSource = {
    type: "text",
    name: name || `Text Data (${format.toUpperCase()})`,
    data: text,
    size: text.length,
    format,
  };

  return { success: true, source };
}

// ============================================================================
// ENHANCED FILE UTILITIES
// ============================================================================

/**
 * Convert File to ImportSource
 */
export function fileToImportSource(file: File): ImportSource {
  const format = file.name.toLowerCase().endsWith(".json")
    ? "json"
    : file.name.toLowerCase().endsWith(".csv")
    ? "csv"
    : "unknown";

  return {
    type: "file",
    name: file.name,
    data: file,
    size: file.size,
    format,
  };
}

/**
 * Process multiple files for import
 */
export function processMultipleFiles(files: FileList | File[]): ImportSource[] {
  const fileArray = Array.from(files);
  return fileArray.map(fileToImportSource);
}

/**
 * Validate import source
 */
export function validateImportSource(source: ImportSource): { isValid: boolean; error?: string } {
  // Check size limits
  const maxSize = 50 * 1024 * 1024; // 50MB
  if (source.size && source.size > maxSize) {
    return {
      isValid: false,
      error: `Source too large: ${(source.size / 1024 / 1024).toFixed(1)}MB (max: 50MB)`,
    };
  }

  // Check format
  if (source.format === "unknown") {
    return {
      isValid: false,
      error: "Unsupported file format. Please use JSON or CSV files.",
    };
  }

  return { isValid: true };
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get import source display name
 */
export function getSourceDisplayName(source: ImportSource): string {
  switch (source.type) {
    case "file":
      return source.name;
    case "url":
      return `URL: ${source.name}`;
    case "clipboard":
      return "Clipboard Data";
    case "text":
      return "Text Input";
    case "database":
      return `Database: ${source.name}`;
    default:
      return "Unknown Source";
  }
}

/**
 * Get import source icon name
 */
export function getSourceIconName(source: ImportSource): string {
  switch (source.type) {
    case "file":
      return "FileText";
    case "url":
      return "Globe";
    case "clipboard":
      return "Clipboard";
    case "text":
      return "Type";
    case "database":
      return "Database";
    default:
      return "File";
  }
}

// ============================================================================
// DATABASE IMPORT UTILITIES
// ============================================================================

/**
 * Create a database import source from search results
 */
export function createDatabaseImportSource(
  searchConfig: DatabaseImportSource,
  selectedDiscs: string, // JSON string of selected discs
  name?: string
): DatabaseImportSourceData {
  return {
    type: "database",
    name: name || `DiscIt API Search (${searchConfig.searchQuery || "All Discs"})`,
    data: selectedDiscs,
    size: selectedDiscs.length,
    format: "json",
    searchConfig,
  };
}
