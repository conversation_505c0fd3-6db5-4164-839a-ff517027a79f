/**
 * Unit tests for Advanced Import Sources with Database Support
 */

import { describe, it, expect } from 'vitest';
import {
  getSourceDisplayName,
  getSourceIconName,
  createDatabaseImportSource,
  type ImportSource,
  type DatabaseImportSource,
  type DatabaseImportSourceData,
  type DataImportSource,
} from '../advancedImportSources';

describe('Advanced Import Sources', () => {
  describe('Type System', () => {
    it('should support all import source types', () => {
      const fileSource: DataImportSource = {
        type: 'file',
        name: 'test.json',
        data: new File(['{}'], 'test.json'),
        format: 'json',
      };

      const urlSource: DataImportSource = {
        type: 'url',
        name: 'data.json',
        data: '{}',
        format: 'json',
      };

      const clipboardSource: DataImportSource = {
        type: 'clipboard',
        name: 'Clipboard Data',
        data: '{}',
        format: 'json',
      };

      const textSource: DataImportSource = {
        type: 'text',
        name: 'Text Input',
        data: '{}',
        format: 'json',
      };

      const databaseSource: DatabaseImportSourceData = {
        type: 'database',
        name: 'DiscIt API Search',
        data: '[]',
        format: 'json',
        searchConfig: {
          type: 'database',
          provider: 'discit-api',
          searchQuery: 'Destroyer',
        },
      };

      // Type assertions to ensure union type works
      const sources: ImportSource[] = [
        fileSource,
        urlSource,
        clipboardSource,
        textSource,
        databaseSource,
      ];

      expect(sources).toHaveLength(5);
    });
  });

  describe('getSourceDisplayName', () => {
    it('should return correct display names for all source types', () => {
      const fileSource: DataImportSource = {
        type: 'file',
        name: 'test.json',
        data: new File(['{}'], 'test.json'),
        format: 'json',
      };

      const urlSource: DataImportSource = {
        type: 'url',
        name: 'data.json',
        data: '{}',
        format: 'json',
      };

      const clipboardSource: DataImportSource = {
        type: 'clipboard',
        name: 'Clipboard Data',
        data: '{}',
        format: 'json',
      };

      const textSource: DataImportSource = {
        type: 'text',
        name: 'Text Input',
        data: '{}',
        format: 'json',
      };

      const databaseSource: DatabaseImportSourceData = {
        type: 'database',
        name: 'DiscIt API Search',
        data: '[]',
        format: 'json',
        searchConfig: {
          type: 'database',
          provider: 'discit-api',
        },
      };

      expect(getSourceDisplayName(fileSource)).toBe('test.json');
      expect(getSourceDisplayName(urlSource)).toBe('URL: data.json');
      expect(getSourceDisplayName(clipboardSource)).toBe('Clipboard Data');
      expect(getSourceDisplayName(textSource)).toBe('Text Input');
      expect(getSourceDisplayName(databaseSource)).toBe('Database: DiscIt API Search');
    });
  });

  describe('getSourceIconName', () => {
    it('should return correct icon names for all source types', () => {
      const fileSource: DataImportSource = {
        type: 'file',
        name: 'test.json',
        data: new File(['{}'], 'test.json'),
        format: 'json',
      };

      const urlSource: DataImportSource = {
        type: 'url',
        name: 'data.json',
        data: '{}',
        format: 'json',
      };

      const clipboardSource: DataImportSource = {
        type: 'clipboard',
        name: 'Clipboard Data',
        data: '{}',
        format: 'json',
      };

      const textSource: DataImportSource = {
        type: 'text',
        name: 'Text Input',
        data: '{}',
        format: 'json',
      };

      const databaseSource: DatabaseImportSourceData = {
        type: 'database',
        name: 'DiscIt API Search',
        data: '[]',
        format: 'json',
        searchConfig: {
          type: 'database',
          provider: 'discit-api',
        },
      };

      expect(getSourceIconName(fileSource)).toBe('FileText');
      expect(getSourceIconName(urlSource)).toBe('Globe');
      expect(getSourceIconName(clipboardSource)).toBe('Clipboard');
      expect(getSourceIconName(textSource)).toBe('Type');
      expect(getSourceIconName(databaseSource)).toBe('Database');
    });
  });

  describe('createDatabaseImportSource', () => {
    it('should create database import source with all fields', () => {
      const searchConfig: DatabaseImportSource = {
        type: 'database',
        provider: 'discit-api',
        searchQuery: 'Destroyer',
        filters: {
          brand: 'Innova',
          category: 'Distance Driver',
          speed: 12,
          stability: 'Overstable',
        },
      };

      const selectedDiscs = JSON.stringify([
        {
          id: '1',
          manufacturer: 'Innova',
          mold: 'Destroyer',
          flightNumbers: { speed: 12, glide: 5, turn: -1, fade: 3 },
        },
      ]);

      const customName = 'Custom Search Results';

      const result = createDatabaseImportSource(searchConfig, selectedDiscs, customName);

      expect(result.type).toBe('database');
      expect(result.name).toBe(customName);
      expect(result.data).toBe(selectedDiscs);
      expect(result.size).toBe(selectedDiscs.length);
      expect(result.format).toBe('json');
      expect(result.searchConfig).toEqual(searchConfig);
    });

    it('should generate default name when not provided', () => {
      const searchConfig: DatabaseImportSource = {
        type: 'database',
        provider: 'discit-api',
        searchQuery: 'Destroyer',
      };

      const selectedDiscs = '[]';

      const result = createDatabaseImportSource(searchConfig, selectedDiscs);

      expect(result.name).toBe('DiscIt API Search (Destroyer)');
    });

    it('should handle empty search query in default name', () => {
      const searchConfig: DatabaseImportSource = {
        type: 'database',
        provider: 'discit-api',
      };

      const selectedDiscs = '[]';

      const result = createDatabaseImportSource(searchConfig, selectedDiscs);

      expect(result.name).toBe('DiscIt API Search (All Discs)');
    });

    it('should calculate correct data size', () => {
      const searchConfig: DatabaseImportSource = {
        type: 'database',
        provider: 'discit-api',
      };

      const selectedDiscs = '{"test": "data"}';

      const result = createDatabaseImportSource(searchConfig, selectedDiscs);

      expect(result.size).toBe(selectedDiscs.length);
    });
  });

  describe('Database Import Filters', () => {
    it('should support all filter options', () => {
      const searchConfig: DatabaseImportSource = {
        type: 'database',
        provider: 'discit-api',
        searchQuery: 'test',
        filters: {
          brand: 'Innova',
          category: 'Distance Driver',
          speed: 12,
          stability: 'Overstable',
        },
      };

      expect(searchConfig.filters?.brand).toBe('Innova');
      expect(searchConfig.filters?.category).toBe('Distance Driver');
      expect(searchConfig.filters?.speed).toBe(12);
      expect(searchConfig.filters?.stability).toBe('Overstable');
    });

    it('should work without filters', () => {
      const searchConfig: DatabaseImportSource = {
        type: 'database',
        provider: 'discit-api',
        searchQuery: 'test',
      };

      expect(searchConfig.filters).toBeUndefined();
    });

    it('should work with partial filters', () => {
      const searchConfig: DatabaseImportSource = {
        type: 'database',
        provider: 'discit-api',
        filters: {
          brand: 'Innova',
          speed: 12,
        },
      };

      expect(searchConfig.filters?.brand).toBe('Innova');
      expect(searchConfig.filters?.speed).toBe(12);
      expect(searchConfig.filters?.category).toBeUndefined();
      expect(searchConfig.filters?.stability).toBeUndefined();
    });
  });

  describe('Type Guards and Validation', () => {
    it('should distinguish between data and database import sources', () => {
      const dataSource: DataImportSource = {
        type: 'file',
        name: 'test.json',
        data: new File(['{}'], 'test.json'),
        format: 'json',
      };

      const databaseSource: DatabaseImportSourceData = {
        type: 'database',
        name: 'DiscIt API Search',
        data: '[]',
        format: 'json',
        searchConfig: {
          type: 'database',
          provider: 'discit-api',
        },
      };

      // Type checking
      if (dataSource.type === 'database') {
        // This should not happen
        expect(true).toBe(false);
      } else {
        expect(dataSource.data).toBeInstanceOf(File);
      }

      if (databaseSource.type === 'database') {
        expect(databaseSource.searchConfig).toBeDefined();
        expect(databaseSource.searchConfig.provider).toBe('discit-api');
      }
    });
  });
});
