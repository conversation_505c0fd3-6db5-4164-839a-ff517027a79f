/**
 * Unit tests for Static Database API
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import {
  searchStaticDatabase,
  getStaticDatabaseProviders,
  getStaticDatabaseMetadata,
  getStaticDatabaseManufacturers,
  getStaticDatabaseCategories,
  clearStaticDatabaseCache,
  type StaticDatabaseProvider,
  type StaticDatabaseFilters,
} from "../staticDatabaseAPI";
import { DiscCondition, Location } from "../types";

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe("Static Database API", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    clearStaticDatabaseCache();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("searchStaticDatabase", () => {
    it("should search PDGA database successfully", async () => {
      const mockPDGAData = [
        {
          manufacturer: "Innova Champion Discs",
          model: "Destroyer",
          approvedDate: "2007-03-15",
          class: "Super Class",
        },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockPDGAData),
      });

      const filters: StaticDatabaseFilters = {
        provider: "pdga",
        manufacturer: "Innova",
      };

      const result = await searchStaticDatabase(filters);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.length).toBe(1);
      expect(result.data![0].manufacturer).toBe("Innova Champion Discs");
      expect(result.data![0].mold).toBe("Destroyer");
      expect(result.data![0].condition).toBe(DiscCondition.NEW);
      expect(result.data![0].currentLocation).toBe(Location.HOME);
    });

    it("should search community database successfully", async () => {
      const mockCommunityData = [
        {
          manufacturer: "Innova",
          mold: "Destroyer",
          speed: 12,
          glide: 5,
          turn: -1,
          fade: 3,
          stability: "Overstable",
          category: "Distance Driver",
          plasticTypes: ["Champion", "Star"],
          averageWeight: 175,
          communityRating: 4.5,
          reviewCount: 1250,
        },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockCommunityData),
      });

      const filters: StaticDatabaseFilters = {
        provider: "community",
        speed: 12,
      };

      const result = await searchStaticDatabase(filters);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.length).toBe(1);
      expect(result.data![0].flightNumbers.speed).toBe(12);
      expect(result.data![0].plasticType).toBe("Champion");
      expect(result.data![0].weight).toBe(175);
    });

    it("should handle manufacturer database with multiple files", async () => {
      const mockInnovaData = [
        {
          manufacturer: "Innova",
          mold: "Destroyer",
          speed: 12,
          glide: 5,
          turn: -1,
          fade: 3,
          category: "Distance Driver",
          plasticTypes: ["Champion", "Star"],
          weightRange: { min: 165, max: 175 },
          stability: "Overstable",
          description: "Fast stable driver",
          discontinued: false,
          releaseYear: 2007,
        },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockInnovaData),
      });

      const filters: StaticDatabaseFilters = {
        provider: "manufacturer",
        includeDiscontinued: false,
      };

      const result = await searchStaticDatabase(filters);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.length).toBe(1);
      expect(result.data![0].weight).toBe(175); // Should use max weight
      expect(result.data![0].notes).toContain("Manufacturer database");
      expect(result.data![0].notes).toContain("Released: 2007");
    });

    it("should filter results by manufacturer", async () => {
      const mockData = [
        {
          manufacturer: "Innova",
          mold: "Destroyer",
          speed: 12,
          glide: 5,
          turn: -1,
          fade: 3,
          stability: "Overstable",
          category: "Distance Driver",
        },
        {
          manufacturer: "Discraft",
          mold: "Buzzz",
          speed: 5,
          glide: 4,
          turn: -1,
          fade: 1,
          stability: "Stable",
          category: "Midrange",
        },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockData),
      });

      const filters: StaticDatabaseFilters = {
        provider: "community",
        manufacturer: "Innova",
      };

      const result = await searchStaticDatabase(filters);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.length).toBe(1);
      expect(result.data![0].manufacturer).toBe("Innova");
    });

    it("should filter results by search query", async () => {
      const mockData = [
        {
          manufacturer: "Innova",
          mold: "Destroyer",
          speed: 12,
          glide: 5,
          turn: -1,
          fade: 3,
          stability: "Overstable",
          category: "Distance Driver",
        },
        {
          manufacturer: "Innova",
          mold: "Aviar",
          speed: 2,
          glide: 3,
          turn: 0,
          fade: 1,
          stability: "Stable",
          category: "Putter",
        },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockData),
      });

      const filters: StaticDatabaseFilters = {
        provider: "community",
        searchQuery: "destroyer",
      };

      const result = await searchStaticDatabase(filters);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.length).toBe(1);
      expect(result.data![0].mold).toBe("Destroyer");
    });

    it("should apply limit to results", async () => {
      const mockData = Array.from({ length: 100 }, (_, i) => ({
        manufacturer: "Innova",
        mold: `Disc${i}`,
        speed: 5,
        glide: 4,
        turn: 0,
        fade: 1,
        stability: "Stable",
        category: "Midrange",
      }));

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockData),
      });

      const filters: StaticDatabaseFilters = {
        provider: "community",
        limit: 10,
      };

      const result = await searchStaticDatabase(filters);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.length).toBe(10);
    });

    it("should handle fetch errors gracefully by falling back to sample data", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Network error"));

      const filters: StaticDatabaseFilters = {
        provider: "pdga",
      };

      const result = await searchStaticDatabase(filters);

      // Should succeed with sample data fallback
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.length).toBeGreaterThan(0);
    });

    it("should handle HTTP errors gracefully by falling back to sample data", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        statusText: "Not Found",
      });

      const filters: StaticDatabaseFilters = {
        provider: "pdga",
      };

      const result = await searchStaticDatabase(filters);

      // Should succeed with sample data fallback
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.length).toBeGreaterThan(0);
    });
  });

  describe("getStaticDatabaseProviders", () => {
    it("should return all available providers", () => {
      const providers = getStaticDatabaseProviders();

      expect(providers).toHaveLength(3);
      expect(providers.map((p) => p.provider)).toEqual(expect.arrayContaining(["pdga", "community", "manufacturer"]));
    });
  });

  describe("getStaticDatabaseMetadata", () => {
    it("should return metadata for valid provider", () => {
      const metadata = getStaticDatabaseMetadata("pdga");

      expect(metadata).toBeDefined();
      expect(metadata!.provider).toBe("pdga");
      expect(metadata!.name).toBe("PDGA Approved Discs");
    });

    it("should return null for invalid provider", () => {
      const metadata = getStaticDatabaseMetadata("invalid" as StaticDatabaseProvider);

      expect(metadata).toBeNull();
    });
  });

  describe("getStaticDatabaseManufacturers", () => {
    it("should return manufacturers for community database", async () => {
      const mockData = [
        { manufacturer: "Innova", mold: "Destroyer" },
        { manufacturer: "Discraft", mold: "Buzzz" },
        { manufacturer: "Innova", mold: "Aviar" },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockData),
      });

      const result = await getStaticDatabaseManufacturers("community");

      expect(result.success).toBe(true);
      expect(result.data).toEqual(["Discraft", "Innova"]);
    });

    it("should handle errors gracefully by falling back to sample data", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Network error"));

      const result = await getStaticDatabaseManufacturers("community");

      // Should succeed with sample data fallback
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
    });
  });

  describe("getStaticDatabaseCategories", () => {
    it("should return categories for community database", async () => {
      const mockData = [
        { category: "Distance Driver", mold: "Destroyer" },
        { category: "Midrange", mold: "Buzzz" },
        { category: "Putter", mold: "Aviar" },
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockData),
      });

      const result = await getStaticDatabaseCategories("community");

      expect(result.success).toBe(true);
      expect(result.data).toEqual(["Distance Driver", "Midrange", "Putter"]);
    });

    it("should handle errors gracefully by falling back to sample data", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Network error"));

      const result = await getStaticDatabaseCategories("community");

      // Should succeed with sample data fallback
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
    });
  });
});
