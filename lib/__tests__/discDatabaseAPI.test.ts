/**
 * Unit tests for DiscIt Database API Integration
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import {
  buildDiscItQuery,
  searchDiscItAPI,
  searchDiscItAPIWithRetry,
  transformDiscItData,
  transformDiscItDataSafe,
  searchAndTransformDiscItAPI,
  getDiscItBrands,
  getDiscItCategories,
  getDiscItStabilityOptions,
  type DiscItDisc,
  type DiscItSearchFilters,
} from "../discDatabaseAPI";
import { DiscCondition, Location } from "../types";

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe("DiscIt Database API", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe("buildDiscItQuery", () => {
    it("should build empty query for no filters", () => {
      const query = buildDiscItQuery({});
      expect(query).toBe("limit=50");
    });

    it("should build query with all filters", () => {
      const filters: DiscItSearchFilters = {
        brand: "Innova",
        category: "Distance Driver",
        speed: 12,
        stability: "Overstable",
        limit: 25,
        offset: 10,
      };

      const query = buildDiscItQuery(filters);
      const params = new URLSearchParams(query);

      expect(params.get("brand")).toBe("Innova");
      expect(params.get("category")).toBe("Distance Driver");
      expect(params.get("speed")).toBe("12");
      expect(params.get("stability")).toBe("Overstable");
      expect(params.get("limit")).toBe("25");
      expect(params.get("offset")).toBe("10");
    });

    it("should respect max limit", () => {
      const query = buildDiscItQuery({ limit: 200 });
      const params = new URLSearchParams(query);
      expect(params.get("limit")).toBe("100"); // Should be capped at maxLimit
    });

    it("should use default limit when not specified", () => {
      const query = buildDiscItQuery({});
      const params = new URLSearchParams(query);
      expect(params.get("limit")).toBe("50");
    });
  });

  describe("searchDiscItAPI", () => {
    const mockApiResponse: DiscItDisc[] = [
      {
        id: "1",
        name: "Destroyer",
        brand: "Innova",
        category: "Distance Driver",
        speed: 12,
        glide: 5,
        turn: -1,
        fade: 3,
        stability: "Overstable",
        link: "https://example.com",
        pic: "https://example.com/pic.jpg",
        nameSlug: "destroyer",
        brandSlug: "innova",
        categorySlug: "distance-driver",
        stabilitySlug: "overstable",
        color: "Red",
        backgroundColorHex: "#ff0000",
        textColorHex: "#ffffff",
      },
    ];

    it("should successfully fetch and return disc data", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse,
      });

      const result = await searchDiscItAPI({ brand: "Innova" });

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockApiResponse);
      expect(result.total).toBe(1);
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("https://discit-api.fly.dev/disc"),
        expect.objectContaining({
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
        })
      );
    });

    it("should handle API errors", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: "Internal Server Error",
      });

      const result = await searchDiscItAPI();

      expect(result.success).toBe(false);
      expect(result.error).toBe("API request failed: 500 Internal Server Error");
    });

    it("should handle network errors", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Network error"));

      const result = await searchDiscItAPI();

      expect(result.success).toBe(false);
      expect(result.error).toBe("Network error");
    });

    it("should handle timeout", async () => {
      mockFetch.mockImplementationOnce(
        () =>
          new Promise((resolve) => {
            // Never resolve to simulate timeout
          })
      );

      const resultPromise = searchDiscItAPI();

      // Fast-forward time to trigger timeout
      vi.advanceTimersByTime(10001);

      const result = await resultPromise;

      expect(result.success).toBe(false);
      expect(result.error).toBe("Request timed out. Please try again.");
    }, 15000); // Increase test timeout
  });

  describe("searchDiscItAPIWithRetry", () => {
    it("should retry on server errors", async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: "Internal Server Error",
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => [],
        });

      const result = await searchDiscItAPIWithRetry({}, 1);

      expect(result.success).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(2);
    }, 15000); // Increase test timeout

    it("should not retry on client errors", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: "Not Found",
      });

      const result = await searchDiscItAPIWithRetry({}, 3);

      expect(result.success).toBe(false);
      expect(result.error).toContain("404");
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });
  });

  describe("transformDiscItData", () => {
    const mockApiDisc: DiscItDisc = {
      id: "1",
      name: "Destroyer",
      brand: "Innova",
      category: "Distance Driver",
      speed: 12,
      glide: 5,
      turn: -1,
      fade: 3,
      stability: "Overstable",
      link: "https://example.com",
      pic: "https://example.com/pic.jpg",
      nameSlug: "destroyer",
      brandSlug: "innova",
      categorySlug: "distance-driver",
      stabilitySlug: "overstable",
      color: "Red",
      backgroundColorHex: "#ff0000",
      textColorHex: "#ffffff",
    };

    it("should transform API disc to internal format", () => {
      const result = transformDiscItData([mockApiDisc]);

      expect(result).toHaveLength(1);
      const disc = result[0];

      expect(disc.manufacturer).toBe("Innova");
      expect(disc.mold).toBe("Destroyer");
      expect(disc.flightNumbers).toEqual({
        speed: 12,
        glide: 5,
        turn: -1,
        fade: 3,
      });
      expect(disc.color).toBe("Red");
      expect(disc.condition).toBe(DiscCondition.NEW);
      expect(disc.currentLocation).toBe(Location.HOME);
      expect(disc.weight).toBe(175);
      expect(disc.plasticType).toBe("Unknown");
      expect(disc.notes).toBe("Imported from DiscIt API. Stability: Overstable");
      expect(disc.imageUrl).toBe("https://example.com/pic.jpg");
      expect(disc.id).toBeDefined();
      expect(disc.createdAt).toBeInstanceOf(Date);
      expect(disc.updatedAt).toBeInstanceOf(Date);
    });

    it("should handle missing optional fields", () => {
      const discWithoutOptionals = {
        ...mockApiDisc,
        color: "",
        pic: "",
      };

      const result = transformDiscItData([discWithoutOptionals]);
      const disc = result[0];

      expect(disc.color).toBe("Unknown");
      expect(disc.imageUrl).toBeUndefined();
    });
  });

  describe("transformDiscItDataSafe", () => {
    it("should handle invalid input", () => {
      const result = transformDiscItDataSafe("invalid" as any);

      expect(result.success).toBe(false);
      expect(result.error).toBe("Invalid API response: expected array of discs");
    });

    it("should handle empty array with non-empty input", () => {
      // Test with invalid disc data that would result in empty transformation
      const invalidDisc = {} as DiscItDisc;

      const result = transformDiscItDataSafe([invalidDisc]);

      // This should succeed but with transformed data
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
    });
  });

  describe("Filter option functions", () => {
    it("should return brand options", async () => {
      const result = await getDiscItBrands();

      expect(result.success).toBe(true);
      expect(result.data).toContain("Innova");
      expect(result.data).toContain("Discraft");
      expect(Array.isArray(result.data)).toBe(true);
    });

    it("should return category options", async () => {
      const result = await getDiscItCategories();

      expect(result.success).toBe(true);
      expect(result.data).toContain("Putter");
      expect(result.data).toContain("Distance Driver");
    });

    it("should return stability options", async () => {
      const result = await getDiscItStabilityOptions();

      expect(result.success).toBe(true);
      expect(result.data).toContain("Overstable");
      expect(result.data).toContain("Understable");
    });
  });

  describe("searchAndTransformDiscItAPI", () => {
    it("should search and transform in one operation", async () => {
      const mockApiResponse: DiscItDisc[] = [
        {
          id: "1",
          name: "Destroyer",
          brand: "Innova",
          category: "Distance Driver",
          speed: 12,
          glide: 5,
          turn: -1,
          fade: 3,
          stability: "Overstable",
          link: "https://example.com",
          pic: "https://example.com/pic.jpg",
          nameSlug: "destroyer",
          brandSlug: "innova",
          categorySlug: "distance-driver",
          stabilitySlug: "overstable",
          color: "Red",
          backgroundColorHex: "#ff0000",
          textColorHex: "#ffffff",
        },
      ];

      // Mock multiple calls for retry logic
      mockFetch.mockResolvedValue({
        ok: true,
        json: async () => mockApiResponse,
      });

      const result = await searchAndTransformDiscItAPI({ brand: "Innova" });

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0].manufacturer).toBe("Innova");
      expect(result.data![0].mold).toBe("Destroyer");
    });

    it("should handle search failure", async () => {
      mockFetch.mockRejectedValue(new Error("Network error"));

      const result = await searchAndTransformDiscItAPI();

      expect(result.success).toBe(false);
      expect(result.error).toContain("Network error");
    });
  });
});
