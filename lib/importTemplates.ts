/**
 * Import Templates & Presets for Disc Golf Inventory Management System
 *
 * This module provides utilities for managing field mapping templates
 * and presets for CSV imports with localStorage persistence.
 */

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Import template definition
 */
export interface ImportTemplate {
  id: string;
  name: string;
  description: string;
  fieldMappings: Record<string, string>;
  fileFormat: "csv" | "json";
  createdAt: Date;
  updatedAt: Date;
  usageCount: number;
  isBuiltIn: boolean;
  tags: string[];
}

/**
 * Template validation result
 */
export interface TemplateValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Template export data
 */
export interface TemplateExportData {
  version: string;
  templates: ImportTemplate[];
  exportedAt: Date;
}

// ============================================================================
// BUILT-IN TEMPLATES
// ============================================================================

/**
 * Built-in templates for common CSV formats
 */
const BUILT_IN_TEMPLATES: Omit<ImportTemplate, "id" | "createdAt" | "updatedAt" | "usageCount">[] = [
  {
    name: "Standard Disc Golf CSV",
    description: "Standard format with manufacturer, mold, plastic, weight, and flight numbers",
    fieldMappings: {
      "Manufacturer": "manufacturer",
      "Mold": "mold",
      "Plastic": "plasticType",
      "Weight": "weight",
      "Color": "color",
      "Condition": "condition",
      "Speed": "flightNumbers.speed",
      "Glide": "flightNumbers.glide",
      "Turn": "flightNumbers.turn",
      "Fade": "flightNumbers.fade",
    },
    fileFormat: "csv",
    isBuiltIn: true,
    tags: ["standard", "flight-numbers"],
  },
  {
    name: "Simple Disc List",
    description: "Basic format with just manufacturer, mold, and plastic type",
    fieldMappings: {
      "Brand": "manufacturer",
      "Disc": "mold",
      "Plastic": "plasticType",
      "Weight (g)": "weight",
    },
    fileFormat: "csv",
    isBuiltIn: true,
    tags: ["simple", "basic"],
  },
  {
    name: "Inventory Tracking",
    description: "Format for tracking disc location and purchase information",
    fieldMappings: {
      "Manufacturer": "manufacturer",
      "Mold": "mold",
      "Plastic Type": "plasticType",
      "Weight": "weight",
      "Location": "currentLocation",
      "Purchase Price": "purchasePrice",
      "Purchase Date": "purchaseDate",
      "Notes": "notes",
    },
    fileFormat: "csv",
    isBuiltIn: true,
    tags: ["inventory", "tracking", "purchase"],
  },
  {
    name: "Flight Numbers Focus",
    description: "Template emphasizing flight characteristics and performance data",
    fieldMappings: {
      "Manufacturer": "manufacturer",
      "Mold": "mold",
      "Plastic": "plasticType",
      "Weight": "weight",
      "Speed": "flightNumbers.speed",
      "Glide": "flightNumbers.glide",
      "Turn": "flightNumbers.turn",
      "Fade": "flightNumbers.fade",
      "Condition": "condition",
      "Notes": "notes",
    },
    fileFormat: "csv",
    isBuiltIn: true,
    tags: ["flight-numbers", "performance"],
  },
];

// ============================================================================
// STORAGE UTILITIES
// ============================================================================

const STORAGE_KEY = "disc-golf-import-templates";
const STORAGE_VERSION = "1.0";

/**
 * Load templates from localStorage
 */
function loadTemplatesFromStorage(): ImportTemplate[] {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return [];

    const data = JSON.parse(stored);
    return data.templates?.map((template: any) => ({
      ...template,
      createdAt: new Date(template.createdAt),
      updatedAt: new Date(template.updatedAt),
    })) || [];
  } catch (error) {
    console.error("Failed to load templates from storage:", error);
    return [];
  }
}

/**
 * Save templates to localStorage
 */
function saveTemplatesToStorage(templates: ImportTemplate[]): void {
  try {
    const data = {
      version: STORAGE_VERSION,
      templates,
      lastUpdated: new Date().toISOString(),
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error("Failed to save templates to storage:", error);
    throw new Error("Failed to save templates");
  }
}

/**
 * Generate unique template ID
 */
function generateTemplateId(): string {
  return `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// ============================================================================
// TEMPLATE MANAGEMENT
// ============================================================================

/**
 * Initialize templates with built-in templates
 */
export function initializeTemplates(): ImportTemplate[] {
  const existingTemplates = loadTemplatesFromStorage();
  const builtInIds = new Set(existingTemplates.filter(t => t.isBuiltIn).map(t => t.name));

  // Add missing built-in templates
  const newBuiltInTemplates = BUILT_IN_TEMPLATES
    .filter(template => !builtInIds.has(template.name))
    .map(template => ({
      ...template,
      id: generateTemplateId(),
      createdAt: new Date(),
      updatedAt: new Date(),
      usageCount: 0,
    }));

  if (newBuiltInTemplates.length > 0) {
    const allTemplates = [...existingTemplates, ...newBuiltInTemplates];
    saveTemplatesToStorage(allTemplates);
    return allTemplates;
  }

  return existingTemplates;
}

/**
 * Get all templates
 */
export function getAllTemplates(): ImportTemplate[] {
  return initializeTemplates();
}

/**
 * Get template by ID
 */
export function getTemplateById(id: string): ImportTemplate | null {
  const templates = getAllTemplates();
  return templates.find(template => template.id === id) || null;
}

/**
 * Search templates by name or tags
 */
export function searchTemplates(query: string): ImportTemplate[] {
  const templates = getAllTemplates();
  const lowerQuery = query.toLowerCase();

  return templates.filter(template =>
    template.name.toLowerCase().includes(lowerQuery) ||
    template.description.toLowerCase().includes(lowerQuery) ||
    template.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
  );
}

/**
 * Create a new template
 */
export function createTemplate(
  templateData: Omit<ImportTemplate, "id" | "createdAt" | "updatedAt" | "usageCount" | "isBuiltIn">
): ImportTemplate {
  const template: ImportTemplate = {
    ...templateData,
    id: generateTemplateId(),
    createdAt: new Date(),
    updatedAt: new Date(),
    usageCount: 0,
    isBuiltIn: false,
  };

  const templates = getAllTemplates();
  templates.push(template);
  saveTemplatesToStorage(templates);

  return template;
}

/**
 * Update an existing template
 */
export function updateTemplate(id: string, updates: Partial<ImportTemplate>): ImportTemplate | null {
  const templates = getAllTemplates();
  const index = templates.findIndex(template => template.id === id);

  if (index === -1) return null;

  // Don't allow updating built-in templates
  if (templates[index].isBuiltIn) {
    throw new Error("Cannot update built-in templates");
  }

  templates[index] = {
    ...templates[index],
    ...updates,
    id, // Ensure ID doesn't change
    updatedAt: new Date(),
    isBuiltIn: templates[index].isBuiltIn, // Preserve built-in status
  };

  saveTemplatesToStorage(templates);
  return templates[index];
}

/**
 * Delete a template
 */
export function deleteTemplate(id: string): boolean {
  const templates = getAllTemplates();
  const index = templates.findIndex(template => template.id === id);

  if (index === -1) return false;

  // Don't allow deleting built-in templates
  if (templates[index].isBuiltIn) {
    throw new Error("Cannot delete built-in templates");
  }

  templates.splice(index, 1);
  saveTemplatesToStorage(templates);
  return true;
}

/**
 * Increment template usage count
 */
export function incrementTemplateUsage(id: string): void {
  const templates = getAllTemplates();
  const template = templates.find(t => t.id === id);

  if (template) {
    template.usageCount++;
    template.updatedAt = new Date();
    saveTemplatesToStorage(templates);
  }
}

// ============================================================================
// TEMPLATE VALIDATION
// ============================================================================

/**
 * Validate a template
 */
export function validateTemplate(template: Partial<ImportTemplate>): TemplateValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!template.name?.trim()) {
    errors.push("Template name is required");
  }

  if (!template.fieldMappings || Object.keys(template.fieldMappings).length === 0) {
    errors.push("At least one field mapping is required");
  }

  // Check for duplicate names (excluding current template)
  if (template.name) {
    const existingTemplates = getAllTemplates();
    const duplicate = existingTemplates.find(t => 
      t.name.toLowerCase() === template.name!.toLowerCase() && 
      t.id !== template.id
    );
    if (duplicate) {
      errors.push("A template with this name already exists");
    }
  }

  // Validate field mappings
  if (template.fieldMappings) {
    const mappedFields = Object.values(template.fieldMappings);
    const duplicateFields = mappedFields.filter((field, index) => 
      field && mappedFields.indexOf(field) !== index
    );

    if (duplicateFields.length > 0) {
      warnings.push("Some fields are mapped multiple times");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// ============================================================================
// IMPORT/EXPORT
// ============================================================================

/**
 * Export templates to JSON
 */
export function exportTemplates(templateIds?: string[]): TemplateExportData {
  const allTemplates = getAllTemplates();
  const templatesToExport = templateIds 
    ? allTemplates.filter(t => templateIds.includes(t.id))
    : allTemplates.filter(t => !t.isBuiltIn); // Don't export built-in templates by default

  return {
    version: STORAGE_VERSION,
    templates: templatesToExport,
    exportedAt: new Date(),
  };
}

/**
 * Import templates from JSON
 */
export function importTemplates(exportData: TemplateExportData): {
  imported: number;
  skipped: number;
  errors: string[];
} {
  const result = {
    imported: 0,
    skipped: 0,
    errors: [] as string[],
  };

  const existingTemplates = getAllTemplates();
  const existingNames = new Set(existingTemplates.map(t => t.name.toLowerCase()));

  for (const templateData of exportData.templates) {
    try {
      // Skip if template with same name exists
      if (existingNames.has(templateData.name.toLowerCase())) {
        result.skipped++;
        continue;
      }

      // Validate template
      const validation = validateTemplate(templateData);
      if (!validation.isValid) {
        result.errors.push(`Template "${templateData.name}": ${validation.errors.join(", ")}`);
        continue;
      }

      // Create new template
      createTemplate({
        name: templateData.name,
        description: templateData.description,
        fieldMappings: templateData.fieldMappings,
        fileFormat: templateData.fileFormat,
        tags: templateData.tags || [],
      });

      result.imported++;
    } catch (error) {
      result.errors.push(`Template "${templateData.name}": ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  return result;
}
