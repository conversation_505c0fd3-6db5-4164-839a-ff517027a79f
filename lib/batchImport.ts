/**
 * Batch Import Utilities for Disc Golf Inventory Management System
 *
 * This module provides utilities for processing multiple files simultaneously
 * with parallel processing, progress tracking, and consolidated results.
 */

import type { Disc } from "./types";
import { importFromFile, type ImportResult, type ImportProgressCallback } from "./exportImport";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Batch import file entry
 */
export interface BatchImportFile {
  id: string;
  file: File;
  fieldMappings?: Record<string, string>;
  status: "pending" | "processing" | "completed" | "failed";
  progress: number;
  result?: ImportResult;
  error?: string;
}

/**
 * Batch import progress data
 */
export interface BatchImportProgress {
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  overallProgress: number;
  currentFile?: string;
  stage: "preparing" | "processing" | "consolidating" | "completed";
}

/**
 * Batch import result
 */
export interface BatchImportResult {
  success: boolean;
  totalFiles: number;
  successfulFiles: number;
  failedFiles: number;
  totalDiscsImported: number;
  consolidatedData: Disc[];
  fileResults: BatchImportFile[];
  errors: string[];
  warnings: string[];
}

/**
 * Batch import options
 */
export interface BatchImportOptions {
  maxConcurrentFiles: number;
  mergeStrategy: "replace" | "merge" | "append";
  skipDuplicates: boolean;
  validateBeforeImport: boolean;
  onProgress?: (progress: BatchImportProgress) => void;
  onFileComplete?: (file: BatchImportFile) => void;
}

// ============================================================================
// BATCH PROCESSING UTILITIES
// ============================================================================

/**
 * Generate unique ID for batch import files
 */
function generateFileId(): string {
  return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Create batch import file entries from File objects
 */
export function createBatchImportFiles(files: File[]): BatchImportFile[] {
  return files.map((file) => ({
    id: generateFileId(),
    file,
    status: "pending",
    progress: 0,
  }));
}

/**
 * Process a single file in the batch
 */
async function processBatchFile(
  batchFile: BatchImportFile,
  currentDiscs: Disc[],
  options: BatchImportOptions
): Promise<BatchImportFile> {
  try {
    batchFile.status = "processing";
    batchFile.progress = 0;

    // Progress callback for individual file
    const progressCallback: ImportProgressCallback = (progressData) => {
      batchFile.progress = progressData.percentage;
      
      // Update overall progress
      if (options.onProgress) {
        // This will be calculated by the batch processor
      }
    };

    // Import the file
    const result = await importFromFile(
      batchFile.file,
      currentDiscs,
      progressCallback,
      batchFile.fieldMappings
    );

    batchFile.result = result;
    batchFile.status = result.success ? "completed" : "failed";
    batchFile.progress = 100;

    if (!result.success) {
      batchFile.error = result.error || "Import failed";
    }

    // Notify completion
    if (options.onFileComplete) {
      options.onFileComplete(batchFile);
    }

    return batchFile;
  } catch (error) {
    batchFile.status = "failed";
    batchFile.error = error instanceof Error ? error.message : "Unknown error";
    batchFile.progress = 0;

    if (options.onFileComplete) {
      options.onFileComplete(batchFile);
    }

    return batchFile;
  }
}

/**
 * Process multiple files in parallel with concurrency control
 */
export async function processBatchImport(
  files: BatchImportFile[],
  currentDiscs: Disc[],
  options: BatchImportOptions
): Promise<BatchImportResult> {
  const startTime = Date.now();
  const totalFiles = files.length;
  let completedFiles = 0;
  let failedFiles = 0;

  // Initialize progress
  const updateProgress = () => {
    const progress: BatchImportProgress = {
      totalFiles,
      completedFiles,
      failedFiles,
      overallProgress: Math.round((completedFiles / totalFiles) * 100),
      stage: completedFiles === totalFiles ? "consolidating" : "processing",
    };

    if (options.onProgress) {
      options.onProgress(progress);
    }
  };

  // Initial progress
  if (options.onProgress) {
    options.onProgress({
      totalFiles,
      completedFiles: 0,
      failedFiles: 0,
      overallProgress: 0,
      stage: "preparing",
    });
  }

  // Process files with concurrency control
  const processQueue = async (fileQueue: BatchImportFile[]): Promise<BatchImportFile[]> => {
    const results: BatchImportFile[] = [];
    const processing: Promise<BatchImportFile>[] = [];

    for (const file of fileQueue) {
      // Wait if we've reached max concurrency
      if (processing.length >= options.maxConcurrentFiles) {
        const completed = await Promise.race(processing);
        const index = processing.findIndex(p => p === Promise.resolve(completed));
        processing.splice(index, 1);
        
        results.push(completed);
        completedFiles++;
        if (completed.status === "failed") failedFiles++;
        updateProgress();
      }

      // Start processing the file
      const filePromise = processBatchFile(file, currentDiscs, options);
      processing.push(filePromise);
    }

    // Wait for remaining files to complete
    const remainingResults = await Promise.all(processing);
    results.push(...remainingResults);
    
    completedFiles = totalFiles;
    failedFiles = results.filter(r => r.status === "failed").length;
    updateProgress();

    return results;
  };

  // Process all files
  const processedFiles = await processQueue(files);

  // Consolidate results
  if (options.onProgress) {
    options.onProgress({
      totalFiles,
      completedFiles,
      failedFiles,
      overallProgress: 100,
      stage: "consolidating",
    });
  }

  const consolidatedResult = consolidateBatchResults(processedFiles, options);

  // Final progress
  if (options.onProgress) {
    options.onProgress({
      totalFiles,
      completedFiles,
      failedFiles,
      overallProgress: 100,
      stage: "completed",
    });
  }

  return consolidatedResult;
}

/**
 * Consolidate results from multiple file imports
 */
function consolidateBatchResults(
  processedFiles: BatchImportFile[],
  options: BatchImportOptions
): BatchImportResult {
  const successfulFiles = processedFiles.filter(f => f.status === "completed");
  const failedFiles = processedFiles.filter(f => f.status === "failed");
  
  // Collect all successfully imported discs
  const allImportedDiscs: Disc[] = [];
  const errors: string[] = [];
  const warnings: string[] = [];

  successfulFiles.forEach(file => {
    if (file.result?.success && file.result.data) {
      allImportedDiscs.push(...file.result.data);
    }
  });

  failedFiles.forEach(file => {
    if (file.error) {
      errors.push(`${file.file.name}: ${file.error}`);
    }
  });

  // Handle duplicates based on strategy
  let consolidatedData = allImportedDiscs;
  if (options.skipDuplicates) {
    const seen = new Set<string>();
    consolidatedData = allImportedDiscs.filter(disc => {
      const key = `${disc.manufacturer}-${disc.mold}-${disc.plasticType}-${disc.weight}`;
      if (seen.has(key)) {
        warnings.push(`Duplicate disc skipped: ${disc.manufacturer} ${disc.mold}`);
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  return {
    success: failedFiles.length === 0,
    totalFiles: processedFiles.length,
    successfulFiles: successfulFiles.length,
    failedFiles: failedFiles.length,
    totalDiscsImported: consolidatedData.length,
    consolidatedData,
    fileResults: processedFiles,
    errors,
    warnings,
  };
}

/**
 * Default batch import options
 */
export const DEFAULT_BATCH_OPTIONS: BatchImportOptions = {
  maxConcurrentFiles: 3,
  mergeStrategy: "merge",
  skipDuplicates: true,
  validateBeforeImport: true,
};
