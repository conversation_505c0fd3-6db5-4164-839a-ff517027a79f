/**
 * Export/Import Functionality for the Disc Golf Inventory Management System
 *
 * This module provides comprehensive data export and import capabilities including
 * JSON export with full data, CSV export for spreadsheet compatibility, JSON import
 * with validation, and error handling for invalid imports.
 */

import type { Disc } from "./types";
import { DiscSchema } from "./validation";
import { exportDiscsToJson, exportDiscsToCSV, createImportSummary } from "./discUtils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Export format options
 */
export type ExportFormat = "json" | "csv";

/**
 * Export options configuration
 */
export interface ExportOptions {
  format: ExportFormat;
  filename?: string;
  includeMetadata?: boolean;
  prettyFormat?: boolean;
}

/**
 * Import result with detailed information
 */
export interface ImportResult {
  success: boolean;
  data?: Disc[];
  error?: string;
  summary?: ImportSummary;
  validationErrors?: ValidationError[];
}

/**
 * Import summary information
 */
export interface ImportSummary {
  totalImported: number;
  totalSkipped: number;
  totalErrors: number;
  newDiscs: number;
  duplicates: number;
  replacements: number;
}

/**
 * Validation error details
 */
export interface ValidationError {
  index: number;
  field?: string;
  message: string;
  value?: unknown;
}

/**
 * File download options
 */
export interface DownloadOptions {
  filename: string;
  mimeType: string;
}

// ============================================================================
// EXPORT FUNCTIONALITY
// ============================================================================

/**
 * Export disc collection with comprehensive options
 *
 * @param discs - Array of discs to export
 * @param options - Export configuration options
 * @returns Export data as string
 */
export function exportCollection(discs: Disc[], options: ExportOptions): string {
  const { format, includeMetadata = true, prettyFormat = true } = options;

  try {
    let exportData: string;

    switch (format) {
      case "json":
        if (includeMetadata) {
          const metadata = {
            exportDate: new Date().toISOString(),
            totalDiscs: discs.length,
            version: "1.0",
            source: "Disc Golf Inventory Management System",
          };

          const dataWithMetadata = {
            metadata,
            discs,
          };

          exportData = JSON.stringify(dataWithMetadata, null, prettyFormat ? 2 : 0);
        } else {
          exportData = exportDiscsToJson(discs, prettyFormat ? 2 : 0);
        }
        break;

      case "csv":
        exportData = exportDiscsToCSV(discs);
        break;

      default:
        throw new Error(`Unsupported export format: ${format}`);
    }

    return exportData;
  } catch (error) {
    console.error("Export failed:", error);
    throw new Error(`Failed to export collection: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Generate filename for export based on current date and format
 *
 * @param format - Export format
 * @param customName - Optional custom filename prefix
 * @returns Generated filename
 */
export function generateExportFilename(format: ExportFormat, customName?: string): string {
  const timestamp = new Date().toISOString().split("T")[0]; // YYYY-MM-DD
  const prefix = customName || "disc-golf-inventory";
  return `${prefix}-${timestamp}.${format}`;
}

/**
 * Download data as file in the browser
 *
 * @param data - Data to download
 * @param options - Download options
 */
export function downloadFile(data: string, options: DownloadOptions): void {
  try {
    const blob = new Blob([data], { type: options.mimeType });
    const url = URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = url;
    link.download = options.filename;
    link.style.display = "none";

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL object
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Download failed:", error);
    throw new Error(`Failed to download file: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Export and download collection as file
 *
 * @param discs - Array of discs to export
 * @param options - Export configuration options
 */
export function exportAndDownload(discs: Disc[], options: ExportOptions): void {
  try {
    const data = exportCollection(discs, options);
    const filename = options.filename || generateExportFilename(options.format);

    const mimeTypes = {
      json: "application/json",
      csv: "text/csv",
    };

    downloadFile(data, {
      filename,
      mimeType: mimeTypes[options.format],
    });
  } catch (error) {
    console.error("Export and download failed:", error);
    throw error;
  }
}

// ============================================================================
// IMPORT FUNCTIONALITY
// ============================================================================

/**
 * Validate disc data using Zod schema
 *
 * @param discs - Array of disc objects to validate
 * @returns Validation result with errors
 */
function validateDiscs(discs: unknown[]): { valid: Disc[]; errors: ValidationError[] } {
  const valid: Disc[] = [];
  const errors: ValidationError[] = [];

  discs.forEach((disc, index) => {
    try {
      // Type guard to ensure disc is an object
      if (!disc || typeof disc !== "object") {
        throw new Error("Invalid disc data: not an object");
      }

      const discObj = disc as Record<string, unknown>;

      // Convert string dates back to Date objects if needed
      const processedDisc = {
        ...discObj,
        purchaseDate: discObj.purchaseDate ? new Date(discObj.purchaseDate as string) : undefined,
        createdAt: new Date(discObj.createdAt as string),
        updatedAt: new Date(discObj.updatedAt as string),
      };

      const validatedDisc = DiscSchema.parse(processedDisc);
      valid.push(validatedDisc);
    } catch (error: unknown) {
      if (error && typeof error === "object" && "errors" in error && Array.isArray(error.errors)) {
        error.errors.forEach((err: unknown) => {
          if (err && typeof err === "object" && "path" in err && "message" in err) {
            errors.push({
              index,
              field: Array.isArray(err.path) ? err.path.join(".") : undefined,
              message: typeof err.message === "string" ? err.message : "Validation error",
              value: "received" in err ? err.received : undefined,
            });
          }
        });
      } else {
        errors.push({
          index,
          message: "Invalid disc data",
          value: disc,
        });
      }
    }
  });

  return { valid, errors };
}

/**
 * Parse and validate JSON import data
 *
 * @param jsonData - JSON string containing disc data
 * @param currentDiscs - Current disc collection for comparison
 * @returns Import result with validation and summary
 */
export function importFromJson(
  jsonData: string,
  currentDiscs: Disc[] = [],
  onProgress?: ImportProgressCallback
): ImportResult {
  try {
    // Parse the JSON first
    if (onProgress) {
      onProgress({
        stage: "parsing",
        percentage: 60,
        message: "Parsing JSON content...",
      });
    }

    const parsed = JSON.parse(jsonData);
    let discsToImport: unknown[];

    // Check if data has metadata wrapper
    if (parsed.metadata && parsed.discs && Array.isArray(parsed.discs)) {
      discsToImport = parsed.discs;
    } else if (Array.isArray(parsed)) {
      discsToImport = parsed;
    } else {
      return {
        success: false,
        error: "Data must be an array of discs or an object with a 'discs' array",
      };
    }

    // Basic validation - check if objects have required disc properties
    const requiredFields = [
      "id",
      "manufacturer",
      "mold",
      "plasticType",
      "weight",
      "condition",
      "flightNumbers",
      "color",
      "currentLocation",
    ];

    for (const item of discsToImport) {
      if (typeof item !== "object" || item === null) {
        return { success: false, error: "Invalid disc object found" };
      }

      for (const field of requiredFields) {
        if (!(field in item)) {
          return { success: false, error: `Missing required field: ${field}` };
        }
      }
    }

    // Validate all discs using Zod schema
    if (onProgress) {
      onProgress({
        stage: "validating",
        percentage: 80,
        message: `Validating ${discsToImport.length} discs...`,
      });
    }

    const { valid: validDiscs, errors: validationErrors } = validateDiscs(discsToImport);

    // Create import summary
    const summary = createImportSummary(currentDiscs, validDiscs);
    const importSummary: ImportSummary = {
      totalImported: validDiscs.length,
      totalSkipped: discsToImport.length - validDiscs.length,
      totalErrors: validationErrors.length,
      newDiscs: summary.newDiscIds.length,
      duplicates: summary.duplicateIds.length,
      replacements: summary.wouldReplace ? summary.duplicateIds.length : 0,
    };

    return {
      success: true,
      data: validDiscs,
      summary: importSummary,
      validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
    };
  } catch (error) {
    return {
      success: false,
      error: `Import failed: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

/**
 * Read file content as text
 *
 * @param file - File object to read
 * @returns Promise resolving to file content
 */
export function readFileAsText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      const result = event.target?.result;
      if (typeof result === "string") {
        resolve(result);
      } else {
        reject(new Error("Failed to read file as text"));
      }
    };

    reader.onerror = () => {
      reject(new Error("File reading failed"));
    };

    reader.readAsText(file);
  });
}

/**
 * Read file as text with progress tracking for large files
 *
 * @param file - File to read
 * @param onProgress - Optional progress callback
 * @returns Promise resolving to file content as string
 */
function readFileAsTextWithProgress(file: File, onProgress?: ImportProgressCallback): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      const result = event.target?.result;
      if (typeof result === "string") {
        if (onProgress) {
          onProgress({
            stage: "reading",
            percentage: 100,
            message: "File read complete",
          });
        }
        resolve(result);
      } else {
        reject(new Error("Failed to read file as text"));
      }
    };

    reader.onprogress = (event) => {
      if (event.lengthComputable && onProgress) {
        const percentage = Math.round((event.loaded / event.total) * 100);
        onProgress({
          stage: "reading",
          percentage: Math.min(percentage, 95), // Cap at 95% until complete
          message: `Reading file... ${percentage}%`,
        });
      }
    };

    reader.onerror = () => {
      reject(new Error("File reading failed"));
    };

    reader.readAsText(file);
  });
}

/**
 * Parse CSV content for preview (headers and sample rows)
 *
 * @param csvContent - CSV string content
 * @returns CSV preview data with headers and sample rows
 */
export function parseCSVForPreview(csvContent: string): {
  headers: string[];
  rows: string[][];
  totalRows: number;
} {
  const lines = csvContent.trim().split("\n");
  if (lines.length < 1) {
    throw new Error("CSV file must contain at least a header row");
  }

  // Parse header row
  const headers = lines[0].split(",").map((h) => h.trim().replace(/"/g, ""));

  // Parse sample rows (first 5 for preview)
  const sampleRows: string[][] = [];
  const maxSampleRows = Math.min(5, lines.length - 1);

  for (let i = 1; i <= maxSampleRows; i++) {
    const values = lines[i].split(",").map((v) => v.trim().replace(/"/g, ""));
    sampleRows.push(values);
  }

  return {
    headers,
    rows: sampleRows,
    totalRows: lines.length - 1, // Exclude header
  };
}

/**
 * Parse CSV content and convert to disc objects with field mapping
 *
 * @param csvContent - CSV string content
 * @param fieldMappings - Mapping of CSV columns to disc fields
 * @returns Array of disc objects
 */
function parseCSVContentWithMapping(csvContent: string, fieldMappings: Record<string, string>): unknown[] {
  const lines = csvContent.trim().split("\n");
  if (lines.length < 2) {
    throw new Error("CSV file must contain at least a header row and one data row");
  }

  // Parse header row
  const headers = lines[0].split(",").map((h) => h.trim().replace(/"/g, ""));

  // Parse data rows
  const discs: unknown[] = [];
  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(",").map((v) => v.trim().replace(/"/g, ""));
    if (values.length !== headers.length) {
      continue; // Skip malformed rows
    }

    const disc: Record<string, unknown> = {};
    headers.forEach((header, index) => {
      const mappedField = fieldMappings[header];
      if (!mappedField) return; // Skip unmapped columns

      const value = values[index];

      // Handle nested flight numbers
      if (mappedField.startsWith("flightNumbers.")) {
        const flightField = mappedField.replace("flightNumbers.", "");
        if (!disc.flightNumbers) {
          disc.flightNumbers = {};
        }
        (disc.flightNumbers as Record<string, unknown>)[flightField] = parseFloat(value) || 0;
      } else {
        // Handle different data types
        if (mappedField === "weight" || mappedField === "purchasePrice") {
          disc[mappedField] = parseFloat(value) || undefined;
        } else if (mappedField === "purchaseDate" || mappedField === "createdAt" || mappedField === "updatedAt") {
          disc[mappedField] = value ? new Date(value) : undefined;
        } else {
          disc[mappedField] = value || undefined;
        }
      }
    });

    discs.push(disc);
  }

  return discs;
}

/**
 * Parse CSV content and convert to disc objects (legacy function)
 *
 * @param csvContent - CSV string content
 * @returns Array of disc objects
 */
function parseCSVContent(csvContent: string): unknown[] {
  const lines = csvContent.trim().split("\n");
  if (lines.length < 2) {
    throw new Error("CSV file must contain at least a header row and one data row");
  }

  // Parse header row
  const headers = lines[0].split(",").map((h) => h.trim().replace(/"/g, ""));

  // Parse data rows
  const discs: unknown[] = [];
  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(",").map((v) => v.trim().replace(/"/g, ""));
    if (values.length !== headers.length) {
      continue; // Skip malformed rows
    }

    const disc: Record<string, unknown> = {};
    headers.forEach((header, index) => {
      const value = values[index];

      // Handle nested flight numbers
      if (header.startsWith("flightNumbers.")) {
        const flightField = header.replace("flightNumbers.", "");
        if (!disc.flightNumbers) {
          disc.flightNumbers = {};
        }
        (disc.flightNumbers as Record<string, unknown>)[flightField] = parseFloat(value) || 0;
      } else {
        // Handle different data types
        if (header === "weight" || header === "purchasePrice") {
          disc[header] = parseFloat(value) || undefined;
        } else if (header === "purchaseDate" || header === "createdAt" || header === "updatedAt") {
          disc[header] = value ? new Date(value) : undefined;
        } else {
          disc[header] = value || undefined;
        }
      }
    });

    discs.push(disc);
  }

  return discs;
}

/**
 * Import collection from CSV content
 *
 * @param csvContent - CSV string containing disc data
 * @param currentDiscs - Current disc collection for comparison
 * @param onProgress - Optional progress callback
 * @param fieldMappings - Optional field mappings for CSV columns
 * @returns Import result with validation and summary
 */
export function importFromCsv(
  csvContent: string,
  currentDiscs: Disc[] = [],
  onProgress?: ImportProgressCallback,
  fieldMappings?: Record<string, string>
): ImportResult {
  try {
    // Parse CSV content
    if (onProgress) {
      onProgress({
        stage: "parsing",
        percentage: 60,
        message: "Parsing CSV content...",
      });
    }

    const discsToImport = fieldMappings
      ? parseCSVContentWithMapping(csvContent, fieldMappings)
      : parseCSVContent(csvContent);

    if (discsToImport.length === 0) {
      return {
        success: false,
        error: "No valid disc data found in CSV file",
      };
    }

    // Validate all discs using Zod schema
    if (onProgress) {
      onProgress({
        stage: "validating",
        percentage: 80,
        message: `Validating ${discsToImport.length} discs...`,
      });
    }

    const { valid: validDiscs, errors: validationErrors } = validateDiscs(discsToImport);

    // Create import summary
    const summary = createImportSummary(currentDiscs, validDiscs);
    const importSummary: ImportSummary = {
      totalImported: validDiscs.length,
      totalSkipped: discsToImport.length - validDiscs.length,
      totalErrors: validationErrors.length,
      newDiscs: summary.newDiscIds.length,
      duplicates: summary.duplicateIds.length,
      replacements: summary.wouldReplace ? summary.duplicateIds.length : 0,
    };

    return {
      success: true,
      data: validDiscs,
      summary: importSummary,
      validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
    };
  } catch (error) {
    return {
      success: false,
      error: `CSV import failed: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

/**
 * Progress callback for file import operations
 */
export type ImportProgressCallback = (progress: {
  stage: "reading" | "parsing" | "validating" | "processing";
  percentage: number;
  message: string;
}) => void;

/**
 * Import collection from file with progress tracking
 *
 * @param file - File object containing disc data
 * @param currentDiscs - Current disc collection for comparison
 * @param onProgress - Optional progress callback for large files
 * @param fieldMappings - Optional field mappings for CSV files
 * @returns Promise resolving to import result
 */
export async function importFromFile(
  file: File,
  currentDiscs: Disc[] = [],
  onProgress?: ImportProgressCallback,
  fieldMappings?: Record<string, string>
): Promise<ImportResult> {
  try {
    const fileName = file.name.toLowerCase();

    // Validate file type
    if (!fileName.endsWith(".json") && !fileName.endsWith(".csv")) {
      return {
        success: false,
        error: "Only JSON and CSV files are supported for import",
      };
    }

    // Check file size and warn for large files
    const fileSizeMB = file.size / (1024 * 1024);
    const isLargeFile = fileSizeMB > 5; // Consider files > 5MB as large

    if (isLargeFile && onProgress) {
      onProgress({
        stage: "reading",
        percentage: 0,
        message: `Reading large file (${fileSizeMB.toFixed(1)}MB)...`,
      });
    }

    // Read file content with progress
    const content = await readFileAsTextWithProgress(file, onProgress);

    if (onProgress) {
      onProgress({
        stage: "parsing",
        percentage: 50,
        message: "Parsing file content...",
      });
    }

    // Import based on file type
    let result: ImportResult;
    if (fileName.endsWith(".csv")) {
      result = importFromCsv(content, currentDiscs, onProgress, fieldMappings);
    } else {
      result = importFromJson(content, currentDiscs, onProgress);
    }

    if (onProgress) {
      onProgress({
        stage: "processing",
        percentage: 100,
        message: "Import complete!",
      });
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: `File import failed: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}
