/**
 * DiscIt Database API Integration for Disc Golf Inventory Management System
 *
 * This module provides integration with the DiscIt API for searching and importing
 * disc golf disc data from external databases.
 */

import type { Disc } from "./types";
import type { DatabaseImportFilters } from "./advancedImportSources";
import { generateDiscId } from "./discUtils";
import { DiscCondition, Location } from "./types";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * DiscIt API response format
 */
export interface DiscItDisc {
  id: string;
  name: string;
  brand: string;
  category: string;
  speed: number;
  glide: number;
  turn: number;
  fade: number;
  stability: string;
  link: string;
  pic: string;
  nameSlug: string;
  brandSlug: string;
  categorySlug: string;
  stabilitySlug: string;
  color: string;
  backgroundColorHex: string;
  textColorHex: string;
}

/**
 * API search filters
 */
export interface DiscItSearchFilters extends DatabaseImportFilters {
  brand?: string;
  category?: string;
  speed?: number;
  stability?: string;
  limit?: number;
  offset?: number;
}

/**
 * API response wrapper
 */
export interface DiscItAPIResponse {
  discs: DiscItDisc[];
  total: number;
  limit: number;
  offset: number;
}

/**
 * API service result
 */
export interface APIServiceResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  total?: number;
}

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * DiscIt API configuration
 */
export const DISCIT_API_CONFIG = {
  baseURL: "https://discit-api.fly.dev",
  endpoints: {
    search: "/disc",
  },
  timeout: 10000, // 10 seconds
  maxRetries: 3,
  defaultLimit: 50,
  maxLimit: 100,
} as const;

// ============================================================================
// QUERY BUILDING
// ============================================================================

/**
 * Build query string from search filters
 */
export function buildDiscItQuery(filters: DiscItSearchFilters): string {
  const params = new URLSearchParams();

  // Add search filters
  if (filters.brand) {
    params.append("brand", filters.brand);
  }

  if (filters.category) {
    params.append("category", filters.category);
  }

  if (filters.speed !== undefined) {
    params.append("speed", filters.speed.toString());
  }

  if (filters.stability) {
    params.append("stability", filters.stability);
  }

  // Add pagination
  if (filters.limit) {
    params.append("limit", Math.min(filters.limit, DISCIT_API_CONFIG.maxLimit).toString());
  } else {
    params.append("limit", DISCIT_API_CONFIG.defaultLimit.toString());
  }

  if (filters.offset) {
    params.append("offset", filters.offset.toString());
  }

  return params.toString();
}

// ============================================================================
// API SERVICE
// ============================================================================

/**
 * Search DiscIt API with filters and error handling
 */
export async function searchDiscItAPI(filters: DiscItSearchFilters = {}): Promise<APIServiceResult<DiscItDisc[]>> {
  try {
    const queryString = buildDiscItQuery(filters);
    const url = `${DISCIT_API_CONFIG.baseURL}${DISCIT_API_CONFIG.endpoints.search}?${queryString}`;

    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), DISCIT_API_CONFIG.timeout);

    const response = await fetch(url, {
      signal: controller.signal,
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      return {
        success: false,
        error: `API request failed: ${response.status} ${response.statusText}`,
      };
    }

    const data: DiscItDisc[] = await response.json();

    return {
      success: true,
      data,
      total: data.length,
    };
  } catch (error) {
    if (error instanceof Error && error.name === "AbortError") {
      return {
        success: false,
        error: "Request timed out. Please try again.",
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to search DiscIt API",
    };
  }
}

/**
 * Search DiscIt API with retry logic
 */
export async function searchDiscItAPIWithRetry(
  filters: DiscItSearchFilters = {},
  retries: number = DISCIT_API_CONFIG.maxRetries
): Promise<APIServiceResult<DiscItDisc[]>> {
  let lastError: string = "";

  for (let attempt = 0; attempt <= retries; attempt++) {
    const result = await searchDiscItAPI(filters);

    if (result.success) {
      return result;
    }

    lastError = result.error || "Unknown error";

    // Don't retry on client errors (4xx)
    if (lastError.includes("400") || lastError.includes("404")) {
      break;
    }

    // Wait before retry (exponential backoff)
    if (attempt < retries) {
      await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }

  return {
    success: false,
    error: `Failed after ${retries + 1} attempts: ${lastError}`,
  };
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get available brands from DiscIt API
 */
export async function getDiscItBrands(): Promise<APIServiceResult<string[]>> {
  // For now, return common brands. In the future, this could be a separate API endpoint
  const commonBrands = [
    "Innova",
    "Discraft",
    "Dynamic Discs",
    "Latitude 64",
    "Westside Discs",
    "Trilogy",
    "MVP",
    "Axiom",
    "Streamline",
    "Prodigy",
    "Kastaplast",
    "Discmania",
    "Gateway",
    "Infinite Discs",
    "DGA",
    "Millennium",
  ];

  return {
    success: true,
    data: commonBrands.sort(),
  };
}

/**
 * Get available categories from DiscIt API
 */
export async function getDiscItCategories(): Promise<APIServiceResult<string[]>> {
  const categories = ["Putter", "Midrange", "Fairway Driver", "Distance Driver"];

  return {
    success: true,
    data: categories,
  };
}

/**
 * Get available stability options from DiscIt API
 */
export async function getDiscItStabilityOptions(): Promise<APIServiceResult<string[]>> {
  const stabilityOptions = ["Very Overstable", "Overstable", "Stable", "Understable", "Very Understable"];

  return {
    success: true,
    data: stabilityOptions,
  };
}

// ============================================================================
// DATA TRANSFORMATION
// ============================================================================

/**
 * Transform DiscIt API disc data to internal Disc format
 */
export function transformDiscItData(apiDiscs: DiscItDisc[]): Disc[] {
  return apiDiscs.map((apiDisc) => {
    const now = new Date();

    return {
      id: generateDiscId(),
      manufacturer: apiDisc.brand,
      mold: apiDisc.name,
      plasticType: "Unknown", // DiscIt API doesn't provide plastic type
      weight: 175, // Default weight, user can modify
      condition: DiscCondition.NEW, // Default to new condition
      flightNumbers: {
        speed: apiDisc.speed,
        glide: apiDisc.glide,
        turn: apiDisc.turn,
        fade: apiDisc.fade,
      },
      color: apiDisc.color || "Unknown",
      notes: `Imported from DiscIt API. Stability: ${apiDisc.stability}`,
      currentLocation: Location.HOME, // Default location
      imageUrl: apiDisc.pic || undefined,
      createdAt: now,
      updatedAt: now,
    };
  });
}

/**
 * Transform and validate DiscIt API data with error handling
 */
export function transformDiscItDataSafe(apiDiscs: DiscItDisc[]): APIServiceResult<Disc[]> {
  try {
    if (!Array.isArray(apiDiscs)) {
      return {
        success: false,
        error: "Invalid API response: expected array of discs",
      };
    }

    const transformedDiscs = transformDiscItData(apiDiscs);

    // Validate that we have valid discs
    if (transformedDiscs.length === 0 && apiDiscs.length > 0) {
      return {
        success: false,
        error: "Failed to transform any discs from API response",
      };
    }

    return {
      success: true,
      data: transformedDiscs,
      total: transformedDiscs.length,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to transform API data",
    };
  }
}

/**
 * Search and transform DiscIt API data in one operation
 */
export async function searchAndTransformDiscItAPI(
  filters: DiscItSearchFilters = {}
): Promise<APIServiceResult<Disc[]>> {
  const searchResult = await searchDiscItAPIWithRetry(filters);

  if (!searchResult.success || !searchResult.data) {
    return {
      success: false,
      error: searchResult.error || "Failed to search DiscIt API",
    };
  }

  return transformDiscItDataSafe(searchResult.data);
}
