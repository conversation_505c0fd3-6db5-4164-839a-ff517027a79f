/**
 * ValidationResults Component for Import Wizard
 *
 * Displays validation errors and provides suggestions for correction.
 */

"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { 
  AlertTriangle, 
  XCircle, 
  ChevronDown, 
  ChevronRight, 
  FileText, 
  ArrowLeft,
  RefreshCw 
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { ValidationResultsProps } from "./types";

// ============================================================================
// HELPER COMPONENTS
// ============================================================================

/**
 * Error category grouping
 */
interface ErrorGroup {
  category: string;
  count: number;
  errors: any[];
  suggestions: string[];
}

/**
 * Error summary card
 */
interface ErrorSummaryProps {
  importResult: any;
}

function ErrorSummary({ importResult }: ErrorSummaryProps) {
  const { error, validationErrors } = importResult;
  const errorCount = validationErrors?.length || 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="text-center space-y-1">
        <div className="text-2xl font-bold text-red-600">{errorCount}</div>
        <div className="text-sm text-muted-foreground">Validation Errors</div>
      </div>
      <div className="text-center space-y-1">
        <div className="text-2xl font-bold text-orange-600">0</div>
        <div className="text-sm text-muted-foreground">Valid Discs</div>
      </div>
      <div className="text-center space-y-1">
        <div className="text-2xl font-bold text-gray-600">0</div>
        <div className="text-sm text-muted-foreground">Imported</div>
      </div>
    </div>
  );
}

/**
 * Error group component
 */
interface ErrorGroupProps {
  group: ErrorGroup;
  isOpen: boolean;
  onToggle: () => void;
}

function ErrorGroupCard({ group, isOpen, onToggle }: ErrorGroupProps) {
  return (
    <Card>
      <Collapsible open={isOpen} onOpenChange={onToggle}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <XCircle className="h-5 w-5 text-red-600" />
                <div>
                  <CardTitle className="text-base">{group.category}</CardTitle>
                  <CardDescription>
                    {group.count} error{group.count !== 1 ? 's' : ''}
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="destructive">{group.count}</Badge>
                {isOpen ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent className="pt-0 space-y-4">
            {/* Error List */}
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {group.errors.map((error, index) => (
                <div key={index} className="p-3 bg-red-50 border border-red-200 rounded text-sm">
                  <div className="font-medium text-red-800">
                    Row {error.index + 1}
                    {error.field && <span className="text-red-600"> • {error.field}</span>}
                  </div>
                  <div className="text-red-700 mt-1">{error.message}</div>
                  {error.value && (
                    <div className="text-red-600 mt-1 text-xs">
                      Value: <code className="bg-red-100 px-1 rounded">{String(error.value)}</code>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Suggestions */}
            {group.suggestions.length > 0 && (
              <div className="space-y-2">
                <h5 className="font-medium text-sm">Suggestions:</h5>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  {group.suggestions.map((suggestion, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <span className="text-blue-600 mt-0.5">•</span>
                      <span>{suggestion}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Group validation errors by category
 */
function groupValidationErrors(validationErrors: any[]): ErrorGroup[] {
  const groups: Record<string, ErrorGroup> = {};

  validationErrors.forEach((error) => {
    const category = error.field ? `${error.field} Errors` : 'General Errors';
    
    if (!groups[category]) {
      groups[category] = {
        category,
        count: 0,
        errors: [],
        suggestions: getSuggestionsForField(error.field),
      };
    }

    groups[category].count++;
    groups[category].errors.push(error);
  });

  return Object.values(groups).sort((a, b) => b.count - a.count);
}

/**
 * Get suggestions for specific field errors
 */
function getSuggestionsForField(field?: string): string[] {
  const suggestions: Record<string, string[]> = {
    manufacturer: [
      'Check spelling of manufacturer names (e.g., "Innova", "Discraft", "Dynamic Discs")',
      'Ensure manufacturer field is not empty',
    ],
    mold: [
      'Verify disc mold names are spelled correctly',
      'Mold names should not be empty',
    ],
    weight: [
      'Weight must be a number between 150 and 180 grams',
      'Use numeric values only (e.g., 175, not "175g")',
    ],
    flightNumbers: [
      'Flight numbers should be in separate columns: flightNumbers.speed, flightNumbers.glide, etc.',
      'All flight numbers must be numeric values',
      'Speed: 1-15, Glide: 1-7, Turn: -5 to +1, Fade: 0-5',
    ],
    condition: [
      'Valid conditions: "new", "good", "fair", "worn", "damaged"',
      'Use lowercase values',
    ],
    currentLocation: [
      'Valid locations: "bag", "car", "home", "loaned", "lost"',
      'Use lowercase values',
    ],
  };

  return suggestions[field || 'general'] || [
    'Check that all required fields are present and properly formatted',
    'Ensure data types match expected formats (numbers for numeric fields, dates for date fields)',
    'Remove any special characters or formatting that might cause parsing issues',
  ];
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * ValidationResults component for displaying import errors
 */
export function ValidationResults({ importResult, onRetry, onCancel, className }: ValidationResultsProps) {
  const [openGroups, setOpenGroups] = React.useState<Set<string>>(new Set());

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const toggleGroup = (category: string) => {
    const newOpenGroups = new Set(openGroups);
    if (newOpenGroups.has(category)) {
      newOpenGroups.delete(category);
    } else {
      newOpenGroups.add(category);
    }
    setOpenGroups(newOpenGroups);
  };

  // ============================================================================
  // DATA PROCESSING
  // ============================================================================

  const errorGroups = importResult.validationErrors 
    ? groupValidationErrors(importResult.validationErrors)
    : [];

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span>Import Failed</span>
          </CardTitle>
          <CardDescription>
            Your file contains validation errors that prevent import. Please review and fix the issues below.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Error Summary */}
          <ErrorSummary importResult={importResult} />

          {/* Main Error Message */}
          {importResult.error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{importResult.error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Error Groups */}
      {errorGroups.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Validation Errors</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setOpenGroups(new Set(errorGroups.map(g => g.category)))}
            >
              Expand All
            </Button>
          </div>
          
          {errorGroups.map((group) => (
            <ErrorGroupCard
              key={group.category}
              group={group}
              isOpen={openGroups.has(group.category)}
              onToggle={() => toggleGroup(group.category)}
            />
          ))}
        </div>
      )}

      {/* General Help */}
      <Alert>
        <FileText className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <div className="font-medium">Need help fixing these errors?</div>
            <ul className="text-sm space-y-1 ml-4">
              <li>• Export a sample file from the Export page to see the correct format</li>
              <li>• Check that CSV headers match expected field names</li>
              <li>• Ensure all required fields (manufacturer, mold, weight, etc.) are present</li>
              <li>• Verify that numeric fields contain only numbers</li>
            </ul>
          </div>
        </AlertDescription>
      </Alert>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onRetry}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Upload
        </Button>
        <div className="space-x-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel Import
          </Button>
          <Button onClick={onRetry}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Different File
          </Button>
        </div>
      </div>
    </div>
  );
}
