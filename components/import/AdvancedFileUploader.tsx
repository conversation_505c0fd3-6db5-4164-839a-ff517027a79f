/**
 * AdvancedFileUploader Component for Import Wizard
 *
 * Enhanced file uploader with support for multiple import sources including
 * files, URLs, clipboard data, and text input.
 */

"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Upload,
  FileText,
  AlertCircle,
  Loader2,
  Globe,
  Clipboard,
  Type,
  CheckCircle,
  Download,
  Database,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  importFromURL,
  importFromClipboard,
  importFromText,
  fileToImportSource,
  validateImportSource,
  getSourceDisplayName,
  isClipboardAvailable,
  createDatabaseImportSource,
  type ImportSource,
} from "@/lib/advancedImportSources";
import { DatabaseSearchTab } from "./DatabaseSearchTab";
import type { Disc } from "@/lib/types";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Advanced file uploader props
 */
export interface AdvancedFileUploaderProps {
  onSourceSelect: (source: ImportSource) => void;
  selectedSource: ImportSource | null;
  isProcessing: boolean;
  error: string | null;
  onNext: () => void;
  onCancel: () => void;
  allowMultiple?: boolean;
  className?: string;
}

// ============================================================================
// HELPER COMPONENTS
// ============================================================================

/**
 * File upload tab component
 */
interface FileUploadTabProps {
  onFileSelect: (files: File[]) => void;
  allowMultiple: boolean;
  className?: string;
}

function FileUploadTab({ onFileSelect, allowMultiple, className }: FileUploadTabProps) {
  const [dragActive, setDragActive] = React.useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      onFileSelect(Array.from(files));
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setDragActive(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragActive(false);

    const files = Array.from(event.dataTransfer.files);
    if (files.length > 0) {
      onFileSelect(files);
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div
        className={cn(
          "relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer",
          dragActive && "border-primary bg-primary/5",
          !dragActive && "border-muted-foreground hover:border-primary hover:bg-muted/50"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleBrowseClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".json,.csv"
          multiple={allowMultiple}
          onChange={handleFileInputChange}
          className="hidden"
        />

        <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <div className="space-y-2">
          <p className="text-lg font-medium">Drop {allowMultiple ? "files" : "file"} here or click to browse</p>
          <p className="text-sm text-muted-foreground">
            Supports JSON and CSV files up to 10MB{allowMultiple ? " (multiple files allowed)" : ""}
          </p>
        </div>
      </div>
    </div>
  );
}

/**
 * URL import tab component
 */
interface URLImportTabProps {
  onURLImport: (url: string) => void;
  isLoading: boolean;
  className?: string;
}

function URLImportTab({ onURLImport, isLoading, className }: URLImportTabProps) {
  const [url, setUrl] = React.useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (url.trim()) {
      onURLImport(url.trim());
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="url-input" className="text-sm font-medium">
            File URL
          </label>
          <Input
            id="url-input"
            type="url"
            placeholder="https://example.com/data.json"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            disabled={isLoading}
          />
          <p className="text-xs text-muted-foreground">
            Enter a direct link to a JSON or CSV file. The file will be downloaded and imported.
          </p>
        </div>
        <Button type="submit" disabled={!url.trim() || isLoading} className="w-full">
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Downloading...
            </>
          ) : (
            <>
              <Download className="h-4 w-4 mr-2" />
              Import from URL
            </>
          )}
        </Button>
      </form>
    </div>
  );
}

/**
 * Clipboard import tab component
 */
interface ClipboardImportTabProps {
  onClipboardImport: () => void;
  isLoading: boolean;
  className?: string;
}

function ClipboardImportTab({ onClipboardImport, isLoading, className }: ClipboardImportTabProps) {
  const clipboardAvailable = isClipboardAvailable();

  return (
    <div className={cn("space-y-4", className)}>
      <div className="text-center space-y-4">
        <Clipboard className="h-12 w-12 mx-auto text-muted-foreground" />
        <div className="space-y-2">
          <p className="text-lg font-medium">Import from Clipboard</p>
          <p className="text-sm text-muted-foreground">
            Copy JSON or CSV data to your clipboard, then click the button below to import it.
          </p>
        </div>

        {clipboardAvailable ? (
          <Button onClick={onClipboardImport} disabled={isLoading} className="w-full">
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Reading Clipboard...
              </>
            ) : (
              <>
                <Clipboard className="h-4 w-4 mr-2" />
                Paste from Clipboard
              </>
            )}
          </Button>
        ) : (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>Clipboard access is not available in this browser or requires HTTPS.</AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
}

/**
 * Text input tab component
 */
interface TextInputTabProps {
  onTextImport: (text: string) => void;
  isLoading: boolean;
  className?: string;
}

function TextInputTab({ onTextImport, isLoading, className }: TextInputTabProps) {
  const [text, setText] = React.useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (text.trim()) {
      onTextImport(text.trim());
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="text-input" className="text-sm font-medium">
            Paste Data
          </label>
          <Textarea
            id="text-input"
            placeholder="Paste your JSON or CSV data here..."
            value={text}
            onChange={(e) => setText(e.target.value)}
            disabled={isLoading}
            rows={8}
          />
          <p className="text-xs text-muted-foreground">Paste JSON or CSV data directly into this field.</p>
        </div>
        <Button type="submit" disabled={!text.trim() || isLoading} className="w-full">
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <Type className="h-4 w-4 mr-2" />
              Import Text Data
            </>
          )}
        </Button>
      </form>
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * AdvancedFileUploader component with multiple import sources
 */
export function AdvancedFileUploader({
  onSourceSelect,
  selectedSource,
  isProcessing,
  error,
  onNext,
  onCancel,
  allowMultiple = false,
  className,
}: AdvancedFileUploaderProps) {
  const [activeTab, setActiveTab] = React.useState("file");
  const [loading, setLoading] = React.useState(false);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleFileSelect = (files: File[]) => {
    if (files.length === 0) return;

    if (allowMultiple) {
      // For multiple files, we'll handle this differently
      // For now, just take the first file
      const source = fileToImportSource(files[0]);
      const validation = validateImportSource(source);

      if (validation.isValid) {
        onSourceSelect(source);
      }
    } else {
      const source = fileToImportSource(files[0]);
      const validation = validateImportSource(source);

      if (validation.isValid) {
        onSourceSelect(source);
      }
    }
  };

  const handleURLImport = async (url: string) => {
    setLoading(true);
    try {
      const result = await importFromURL(url);
      if (result.success && result.source) {
        onSourceSelect(result.source);
      }
    } catch (error) {
      console.error("URL import failed:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleClipboardImport = async () => {
    setLoading(true);
    try {
      const result = await importFromClipboard();
      if (result.success && result.source) {
        onSourceSelect(result.source);
      }
    } catch (error) {
      console.error("Clipboard import failed:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleTextImport = (text: string) => {
    setLoading(true);
    try {
      const result = importFromText(text);
      if (result.success && result.source) {
        onSourceSelect(result.source);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDatabaseImport = (discs: Disc[]) => {
    setLoading(true);
    try {
      // Convert selected discs to JSON string
      const discsJson = JSON.stringify(discs);

      // Create database import source
      const source = createDatabaseImportSource(
        {
          type: "database",
          provider: "discit-api",
        },
        discsJson,
        `DiscIt API Import (${discs.length} discs)`
      );

      onSourceSelect(source);
    } finally {
      setLoading(false);
    }
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  const canProceed = selectedSource && !isProcessing && !loading;

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle>Select Import Source</CardTitle>
          <CardDescription>
            Choose how you want to import your disc golf collection data. Multiple sources are supported for maximum
            flexibility.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="file" className="flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <span className="hidden sm:inline">Files</span>
              </TabsTrigger>
              <TabsTrigger value="url" className="flex items-center space-x-2">
                <Globe className="h-4 w-4" />
                <span className="hidden sm:inline">URL</span>
              </TabsTrigger>
              <TabsTrigger value="clipboard" className="flex items-center space-x-2">
                <Clipboard className="h-4 w-4" />
                <span className="hidden sm:inline">Clipboard</span>
              </TabsTrigger>
              <TabsTrigger value="text" className="flex items-center space-x-2">
                <Type className="h-4 w-4" />
                <span className="hidden sm:inline">Text</span>
              </TabsTrigger>
              <TabsTrigger value="database" className="flex items-center space-x-2">
                <Database className="h-4 w-4" />
                <span className="hidden sm:inline">Database</span>
              </TabsTrigger>
            </TabsList>

            <div className="mt-6">
              <TabsContent value="file">
                <FileUploadTab onFileSelect={handleFileSelect} allowMultiple={allowMultiple} />
              </TabsContent>

              <TabsContent value="url">
                <URLImportTab onURLImport={handleURLImport} isLoading={loading} />
              </TabsContent>

              <TabsContent value="clipboard">
                <ClipboardImportTab onClipboardImport={handleClipboardImport} isLoading={loading} />
              </TabsContent>

              <TabsContent value="text">
                <TextInputTab onTextImport={handleTextImport} isLoading={loading} />
              </TabsContent>

              <TabsContent value="database">
                <DatabaseSearchTab onDiscSelect={handleDatabaseImport} isLoading={loading} />
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>
      </Card>

      {/* Selected Source Display */}
      {selectedSource && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-3">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div className="flex-1">
                <div className="font-medium">{getSourceDisplayName(selectedSource)}</div>
                <div className="text-sm text-muted-foreground">
                  {selectedSource.format?.toUpperCase()} format
                  {selectedSource.size && ` • ${(selectedSource.size / 1024).toFixed(1)} KB`}
                </div>
              </div>
              <Badge variant="outline">{selectedSource.type}</Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={onNext} disabled={!canProceed} className="min-w-[120px]">
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            "Next: Preview Data"
          )}
        </Button>
      </div>
    </div>
  );
}
