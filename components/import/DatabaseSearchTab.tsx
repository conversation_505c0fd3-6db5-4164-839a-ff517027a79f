/**
 * DatabaseSearchTab Component for Disc Golf Inventory Management System
 *
 * A search interface for importing disc data from external databases like DiscIt API.
 * Provides filtering capabilities and disc selection for import.
 */

"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";

import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";

import { Search, Loader2, AlertCircle, Database, CheckCircle, X } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  searchAndTransformDiscItAPI,
  getDiscItBrands,
  getDiscItCategories,
  getDiscItStabilityOptions,
  type DiscItSearchFilters,
} from "@/lib/discDatabaseAPI";
import type { Disc } from "@/lib/types";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Props for the DatabaseSearchTab component
 */
export interface DatabaseSearchTabProps {
  onDiscSelect: (discs: Disc[]) => void;
  isLoading: boolean;
  className?: string;
}

/**
 * Search state interface
 */
interface SearchState {
  query: string;
  filters: DiscItSearchFilters;
  results: Disc[];
  selectedDiscs: Set<string>;
  isSearching: boolean;
  error: string | null;
  hasSearched: boolean;
}

/**
 * Filter options interface
 */
interface FilterOptions {
  brands: string[];
  categories: string[];
  stabilityOptions: string[];
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * DatabaseSearchTab component for searching external disc databases
 */
export function DatabaseSearchTab({ onDiscSelect, isLoading, className }: DatabaseSearchTabProps) {
  // ============================================================================
  // STATE
  // ============================================================================

  const [searchState, setSearchState] = React.useState<SearchState>({
    query: "",
    filters: {},
    results: [],
    selectedDiscs: new Set(),
    isSearching: false,
    error: null,
    hasSearched: false,
  });

  const [filterOptions, setFilterOptions] = React.useState<FilterOptions>({
    brands: [],
    categories: [],
    stabilityOptions: [],
  });

  // ============================================================================
  // EFFECTS
  // ============================================================================

  // Load filter options on mount
  React.useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        const [brandsResult, categoriesResult, stabilityResult] = await Promise.all([
          getDiscItBrands(),
          getDiscItCategories(),
          getDiscItStabilityOptions(),
        ]);

        setFilterOptions({
          brands: brandsResult.data || [],
          categories: categoriesResult.data || [],
          stabilityOptions: stabilityResult.data || [],
        });
      } catch (error) {
        console.error("Failed to load filter options:", error);
      }
    };

    loadFilterOptions();
  }, []);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  /**
   * Handle search execution
   */
  const handleSearch = async () => {
    setSearchState((prev) => ({
      ...prev,
      isSearching: true,
      error: null,
    }));

    try {
      const searchFilters: DiscItSearchFilters = {
        ...searchState.filters,
        limit: 50, // Reasonable limit for UI
      };

      const result = await searchAndTransformDiscItAPI(searchFilters);

      if (result.success && result.data) {
        setSearchState((prev) => ({
          ...prev,
          results: result.data || [],
          hasSearched: true,
          isSearching: false,
          selectedDiscs: new Set(), // Reset selection
        }));
      } else {
        setSearchState((prev) => ({
          ...prev,
          error: result.error || "Search failed",
          isSearching: false,
          hasSearched: true,
        }));
      }
    } catch (error) {
      setSearchState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "Search failed",
        isSearching: false,
        hasSearched: true,
      }));
    }
  };

  /**
   * Handle filter changes
   */
  const handleFilterChange = (key: keyof DiscItSearchFilters, value: string | number | undefined) => {
    setSearchState((prev) => ({
      ...prev,
      filters: {
        ...prev.filters,
        [key]: value,
      },
    }));
  };

  /**
   * Handle disc selection toggle
   */
  const handleDiscToggle = (discId: string) => {
    setSearchState((prev) => {
      const newSelected = new Set(prev.selectedDiscs);
      if (newSelected.has(discId)) {
        newSelected.delete(discId);
      } else {
        newSelected.add(discId);
      }
      return {
        ...prev,
        selectedDiscs: newSelected,
      };
    });
  };

  /**
   * Handle select all toggle
   */
  const handleSelectAll = () => {
    setSearchState((prev) => {
      const allSelected = prev.selectedDiscs.size === prev.results.length;
      return {
        ...prev,
        selectedDiscs: allSelected ? new Set() : new Set(prev.results.map((d) => d.id)),
      };
    });
  };

  /**
   * Handle import selected discs
   */
  const handleImportSelected = () => {
    const selectedDiscData = searchState.results.filter((disc) => searchState.selectedDiscs.has(disc.id));
    onDiscSelect(selectedDiscData);
  };

  /**
   * Clear search results
   */
  const handleClearResults = () => {
    setSearchState((prev) => ({
      ...prev,
      results: [],
      selectedDiscs: new Set(),
      hasSearched: false,
      error: null,
    }));
  };

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const hasResults = searchState.results.length > 0;
  const hasSelection = searchState.selectedDiscs.size > 0;
  const allSelected = hasResults && searchState.selectedDiscs.size === searchState.results.length;

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className={cn("space-y-6", className)}>
      {/* Search Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>DiscIt Database Search</span>
          </CardTitle>
          <CardDescription>
            Search the DiscIt database for disc golf discs to import into your collection.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Brand Filter */}
            <div className="space-y-2">
              <Label htmlFor="brand-select">Brand</Label>
              <select
                id="brand-select"
                className="w-full px-3 py-2 border border-input rounded-md bg-background"
                value={searchState.filters.brand || ""}
                onChange={(e) => handleFilterChange("brand", e.target.value || undefined)}
              >
                <option value="">All Brands</option>
                {filterOptions.brands.map((brand) => (
                  <option key={brand} value={brand}>
                    {brand}
                  </option>
                ))}
              </select>
            </div>

            {/* Category Filter */}
            <div className="space-y-2">
              <Label htmlFor="category-select">Category</Label>
              <select
                id="category-select"
                className="w-full px-3 py-2 border border-input rounded-md bg-background"
                value={searchState.filters.category || ""}
                onChange={(e) => handleFilterChange("category", e.target.value || undefined)}
              >
                <option value="">All Categories</option>
                {filterOptions.categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            {/* Speed Filter */}
            <div className="space-y-2">
              <Label htmlFor="speed-select">Speed</Label>
              <select
                id="speed-select"
                className="w-full px-3 py-2 border border-input rounded-md bg-background"
                value={searchState.filters.speed || ""}
                onChange={(e) => handleFilterChange("speed", e.target.value ? parseInt(e.target.value) : undefined)}
              >
                <option value="">Any Speed</option>
                {Array.from({ length: 15 }, (_, i) => i + 1).map((speed) => (
                  <option key={speed} value={speed}>
                    {speed}
                  </option>
                ))}
              </select>
            </div>

            {/* Stability Filter */}
            <div className="space-y-2">
              <Label htmlFor="stability-select">Stability</Label>
              <select
                id="stability-select"
                className="w-full px-3 py-2 border border-input rounded-md bg-background"
                value={searchState.filters.stability || ""}
                onChange={(e) => handleFilterChange("stability", e.target.value || undefined)}
              >
                <option value="">Any Stability</option>
                {filterOptions.stabilityOptions.map((stability) => (
                  <option key={stability} value={stability}>
                    {stability}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Search Button */}
          <div className="flex items-center space-x-4">
            <Button
              onClick={handleSearch}
              disabled={searchState.isSearching || isLoading}
              className="flex items-center space-x-2"
            >
              {searchState.isSearching ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Searching...</span>
                </>
              ) : (
                <>
                  <Search className="h-4 w-4" />
                  <span>Search Discs</span>
                </>
              )}
            </Button>

            {hasResults && (
              <Button variant="outline" onClick={handleClearResults}>
                <X className="h-4 w-4 mr-2" />
                Clear Results
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {searchState.error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{searchState.error}</AlertDescription>
        </Alert>
      )}

      {/* Results Section */}
      {searchState.hasSearched && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Search Results</CardTitle>
                <CardDescription>
                  {hasResults
                    ? `Found ${searchState.results.length} discs. Select discs to import.`
                    : "No discs found matching your search criteria."}
                </CardDescription>
              </div>

              {hasResults && (
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" onClick={handleSelectAll}>
                    {allSelected ? "Deselect All" : "Select All"}
                  </Button>

                  {hasSelection && (
                    <Button onClick={handleImportSelected} disabled={isLoading} className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4" />
                      <span>Import Selected ({searchState.selectedDiscs.size})</span>
                    </Button>
                  )}
                </div>
              )}
            </div>
          </CardHeader>

          {hasResults && (
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                {searchState.results.map((disc) => (
                  <div
                    key={disc.id}
                    className={cn(
                      "border rounded-lg p-4 cursor-pointer transition-colors",
                      searchState.selectedDiscs.has(disc.id)
                        ? "border-primary bg-primary/5"
                        : "border-border hover:border-primary/50"
                    )}
                    onClick={() => handleDiscToggle(disc.id)}
                  >
                    <div className="flex items-start space-x-3">
                      <input
                        type="checkbox"
                        checked={searchState.selectedDiscs.has(disc.id)}
                        onChange={() => handleDiscToggle(disc.id)}
                        className="mt-1 rounded border-input text-primary focus:ring-primary focus:ring-2"
                      />

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-medium truncate">{disc.mold}</h4>
                          <Badge variant="secondary" className="text-xs">
                            {disc.manufacturer}
                          </Badge>
                        </div>

                        <div className="text-sm text-muted-foreground space-y-1">
                          <div className="flex items-center space-x-4">
                            <span>Speed: {disc.flightNumbers.speed}</span>
                            <span>Glide: {disc.flightNumbers.glide}</span>
                            <span>Turn: {disc.flightNumbers.turn}</span>
                            <span>Fade: {disc.flightNumbers.fade}</span>
                          </div>

                          {disc.color && disc.color !== "Unknown" && <div>Color: {disc.color}</div>}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          )}
        </Card>
      )}
    </div>
  );
}
