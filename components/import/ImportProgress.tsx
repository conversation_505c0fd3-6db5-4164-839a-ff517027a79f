/**
 * ImportProgress Component
 *
 * Displays progress indicator for file import operations with detailed
 * stage information and progress percentage.
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Loader2, FileText, CheckCircle, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Import progress data
 */
export interface ImportProgressData {
  stage: "reading" | "parsing" | "validating" | "processing";
  percentage: number;
  message: string;
}

/**
 * Import progress props
 */
export interface ImportProgressProps {
  progress: ImportProgressData | null;
  isVisible: boolean;
  error?: string | null;
  onCancel?: () => void;
  className?: string;
}

// ============================================================================
// STAGE CONFIGURATION
// ============================================================================

/**
 * Stage configuration for progress display
 */
interface StageConfig {
  key: ImportProgressData["stage"];
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

const PROGRESS_STAGES: StageConfig[] = [
  {
    key: "reading",
    title: "Reading File",
    description: "Loading file content from disk",
    icon: FileText,
  },
  {
    key: "parsing",
    title: "Parsing Data",
    description: "Converting file content to disc data",
    icon: Loader2,
  },
  {
    key: "validating",
    title: "Validating",
    description: "Checking data integrity and format",
    icon: CheckCircle,
  },
  {
    key: "processing",
    title: "Processing",
    description: "Finalizing import operation",
    icon: CheckCircle,
  },
];

// ============================================================================
// HELPER COMPONENTS
// ============================================================================

/**
 * Stage indicator component
 */
interface StageIndicatorProps {
  stage: StageConfig;
  isActive: boolean;
  isCompleted: boolean;
  className?: string;
}

function StageIndicator({ stage, isActive, isCompleted, className }: StageIndicatorProps) {
  const Icon = stage.icon;

  return (
    <div className={cn("flex items-center space-x-3", className)}>
      <div
        className={cn(
          "flex h-8 w-8 items-center justify-center rounded-full border-2 transition-colors",
          isActive && "border-primary bg-primary text-primary-foreground",
          isCompleted && "border-green-500 bg-green-500 text-white",
          !isActive && !isCompleted && "border-muted-foreground text-muted-foreground"
        )}
      >
        <Icon className={cn("h-4 w-4", isActive && Icon === Loader2 && "animate-spin")} />
      </div>
      <div className="flex-1">
        <div
          className={cn(
            "text-sm font-medium",
            isActive && "text-foreground",
            isCompleted && "text-green-600",
            !isActive && !isCompleted && "text-muted-foreground"
          )}
        >
          {stage.title}
        </div>
        <div className="text-xs text-muted-foreground">{stage.description}</div>
      </div>
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * ImportProgress component for displaying import operation progress
 */
export function ImportProgress({ progress, isVisible, error, onCancel, className }: ImportProgressProps) {
  if (!isVisible) return null;

  const currentStageIndex = progress ? PROGRESS_STAGES.findIndex((stage) => stage.key === progress.stage) : -1;

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          {error ? (
            <>
              <AlertCircle className="h-5 w-5 text-red-600" />
              <span>Import Failed</span>
            </>
          ) : (
            <>
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
              <span>Importing Collection</span>
            </>
          )}
        </CardTitle>
        <CardDescription>
          {error ? "An error occurred during import" : "Please wait while we process your file..."}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress Bar */}
        {progress && !error && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{progress.message}</span>
              <span>{progress.percentage}%</span>
            </div>
            <Progress value={progress.percentage} className="h-2" />
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded text-sm text-red-800">
            {error}
          </div>
        )}

        {/* Stage Indicators */}
        <div className="space-y-4">
          {PROGRESS_STAGES.map((stage, index) => {
            const isActive = currentStageIndex === index;
            const isCompleted = currentStageIndex > index;

            return (
              <StageIndicator
                key={stage.key}
                stage={stage}
                isActive={isActive}
                isCompleted={isCompleted}
              />
            );
          })}
        </div>

        {/* Cancel Button */}
        {onCancel && !error && (
          <div className="flex justify-center">
            <button
              onClick={onCancel}
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              Cancel Import
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Hook for managing import progress state
 */
export function useImportProgress() {
  const [progress, setProgress] = React.useState<ImportProgressData | null>(null);
  const [isVisible, setIsVisible] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const startProgress = React.useCallback(() => {
    setProgress(null);
    setError(null);
    setIsVisible(true);
  }, []);

  const updateProgress = React.useCallback((progressData: ImportProgressData) => {
    setProgress(progressData);
    setError(null);
  }, []);

  const setProgressError = React.useCallback((errorMessage: string) => {
    setError(errorMessage);
    setProgress(null);
  }, []);

  const hideProgress = React.useCallback(() => {
    setIsVisible(false);
    setProgress(null);
    setError(null);
  }, []);

  return {
    progress,
    isVisible,
    error,
    startProgress,
    updateProgress,
    setProgressError,
    hideProgress,
  };
}
