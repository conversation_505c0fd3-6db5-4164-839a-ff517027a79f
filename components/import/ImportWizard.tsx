/**
 * ImportWizard Component for Disc Golf Inventory Management System
 *
 * A multi-step wizard for importing disc collection data with file upload,
 * validation, preview, and merge options.
 */

"use client";

import * as React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Upload, Map, Eye, Settings, Download } from "lucide-react";
import { cn } from "@/lib/utils";
import { importFromFile, parseCSVForPreview, type ImportProgressCallback } from "@/lib/exportImport";
import { FileUploader } from "./FileUploader";
import { DataPreview } from "./DataPreview";
import { MergeOptions } from "./MergeOptions";
import { ValidationResults } from "./ValidationResults";
import { FieldMapping } from "./FieldMapping";
import { ImportProgress, useImportProgress } from "./ImportProgress";
import type { ImportWizardProps, ImportWizardState, ImportStep, ImportFileFormat, MergeStrategy } from "./types";

// ============================================================================
// STEP CONFIGURATION
// ============================================================================

/**
 * Step configuration for the wizard
 */
interface StepConfig {
  key: ImportStep;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

const IMPORT_STEPS: StepConfig[] = [
  {
    key: "upload",
    title: "Upload File",
    description: "Select your JSON or CSV file",
    icon: Upload,
  },
  {
    key: "mapping",
    title: "Field Mapping",
    description: "Map CSV columns to fields",
    icon: Map,
  },
  {
    key: "preview",
    title: "Preview Data",
    description: "Review imported data",
    icon: Eye,
  },
  {
    key: "options",
    title: "Import Options",
    description: "Choose merge strategy",
    icon: Settings,
  },
  {
    key: "import",
    title: "Import",
    description: "Complete the import",
    icon: Download,
  },
];

// ============================================================================
// PROGRESS INDICATOR
// ============================================================================

interface ImportProgressIndicatorProps {
  currentStep: ImportStep;
  className?: string;
}

function ImportProgressIndicator({ currentStep, className }: ImportProgressIndicatorProps) {
  const currentIndex = IMPORT_STEPS.findIndex((step) => step.key === currentStep);

  return (
    <div className={cn("flex items-center justify-center space-x-4", className)}>
      {IMPORT_STEPS.map((step, index) => {
        const isActive = index === currentIndex;
        const isCompleted = index < currentIndex;
        const Icon = step.icon;

        return (
          <React.Fragment key={step.key}>
            <div className="flex items-center space-x-2">
              <div
                className={cn(
                  "flex h-8 w-8 items-center justify-center rounded-full border-2 transition-colors",
                  isActive && "border-primary bg-primary text-primary-foreground",
                  isCompleted && "border-green-500 bg-green-500 text-white",
                  !isActive && !isCompleted && "border-muted-foreground text-muted-foreground"
                )}
              >
                <Icon className="h-4 w-4" />
              </div>
              <div className="hidden sm:block">
                <div
                  className={cn(
                    "text-sm font-medium",
                    isActive && "text-foreground",
                    isCompleted && "text-green-600",
                    !isActive && !isCompleted && "text-muted-foreground"
                  )}
                >
                  {step.title}
                </div>
              </div>
            </div>
            {index < IMPORT_STEPS.length - 1 && (
              <div className={cn("h-px w-8 transition-colors", isCompleted ? "bg-green-500" : "bg-muted")} />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * ImportWizard component for managing the multi-step import process
 */
export function ImportWizard({ currentDiscs, onImportComplete, onCancel, className }: ImportWizardProps) {
  // ============================================================================
  // STATE
  // ============================================================================

  const [wizardState, setWizardState] = React.useState<ImportWizardState>({
    currentStep: "upload",
    selectedFile: null,
    fileFormat: null,
    csvData: null,
    fieldMappings: null,
    importResult: null,
    mergeStrategy: "merge",
    isProcessing: false,
    error: null,
  });

  // Progress tracking for large files
  const {
    progress,
    isVisible: showProgress,
    error: progressError,
    startProgress,
    updateProgress,
    setProgressError,
    hideProgress,
  } = useImportProgress();

  // ============================================================================
  // HANDLERS
  // ============================================================================

  /**
   * Update wizard state
   */
  const updateState = (updates: Partial<ImportWizardState>) => {
    setWizardState((prev) => ({ ...prev, ...updates }));
  };

  /**
   * Handle file selection
   */
  const handleFileSelect = (file: File) => {
    const format: ImportFileFormat = file.name.toLowerCase().endsWith(".csv") ? "csv" : "json";
    updateState({
      selectedFile: file,
      fileFormat: format,
      importResult: null,
      error: null,
    });
  };

  /**
   * Process the selected file with progress tracking
   */
  const handleProcessFile = async () => {
    if (!wizardState.selectedFile) return;

    updateState({ isProcessing: true, error: null });

    try {
      // For CSV files, parse for field mapping first
      if (wizardState.fileFormat === "csv") {
        const content = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target?.result as string);
          reader.onerror = reject;
          reader.readAsText(wizardState.selectedFile!);
        });
        const csvData = parseCSVForPreview(content);

        updateState({
          csvData,
          currentStep: "mapping",
        });
      } else {
        // For JSON files, proceed directly to import
        await processFileForImport();
      }
    } catch (error) {
      updateState({
        error: error instanceof Error ? error.message : "Failed to process file",
      });
    } finally {
      updateState({ isProcessing: false });
    }
  };

  /**
   * Process file for import (after field mapping for CSV)
   */
  const processFileForImport = async () => {
    if (!wizardState.selectedFile) return;

    updateState({ isProcessing: true, error: null });

    // Check if file is large enough to show progress
    const fileSizeMB = wizardState.selectedFile.size / (1024 * 1024);
    const shouldShowProgress = fileSizeMB > 1; // Show progress for files > 1MB

    if (shouldShowProgress) {
      startProgress();
    }

    try {
      const progressCallback: ImportProgressCallback | undefined = shouldShowProgress
        ? (progressData) => {
            updateProgress(progressData);
          }
        : undefined;

      const result = await importFromFile(
        wizardState.selectedFile,
        currentDiscs,
        progressCallback,
        wizardState.fieldMappings || undefined
      );

      if (shouldShowProgress) {
        hideProgress();
      }

      updateState({
        importResult: result,
        currentStep: result.success ? "preview" : "upload",
        error: result.success ? null : result.error || "Import failed",
      });
    } catch (error) {
      if (shouldShowProgress) {
        setProgressError(error instanceof Error ? error.message : "Import failed");
        setTimeout(hideProgress, 2000); // Hide after 2 seconds
      }

      updateState({
        error: error instanceof Error ? error.message : "Import failed",
      });
    } finally {
      updateState({ isProcessing: false });
    }
  };

  /**
   * Handle field mapping completion
   */
  const handleFieldMappingComplete = (mappings: Record<string, string>) => {
    updateState({ fieldMappings: mappings });
    processFileForImport();
  };

  /**
   * Navigate to next step
   */
  const handleNext = () => {
    const currentIndex = IMPORT_STEPS.findIndex((step) => step.key === wizardState.currentStep);
    if (currentIndex < IMPORT_STEPS.length - 1) {
      const nextStep = IMPORT_STEPS[currentIndex + 1].key;
      updateState({ currentStep: nextStep });
    }
  };

  /**
   * Navigate to previous step
   */
  const handlePrevious = () => {
    const currentIndex = IMPORT_STEPS.findIndex((step) => step.key === wizardState.currentStep);
    if (currentIndex > 0) {
      const prevStep = IMPORT_STEPS[currentIndex - 1].key;
      updateState({ currentStep: prevStep });
    }
  };

  /**
   * Complete the import process
   */
  const handleCompleteImport = async () => {
    if (!wizardState.importResult?.data) return;

    updateState({ isProcessing: true });

    try {
      await onImportComplete(wizardState.importResult.data, wizardState.mergeStrategy);
    } catch (error) {
      updateState({
        error: error instanceof Error ? error.message : "Import failed",
        isProcessing: false,
      });
    }
  };

  // ============================================================================
  // RENDER STEP CONTENT
  // ============================================================================

  const renderStepContent = () => {
    switch (wizardState.currentStep) {
      case "upload":
        return (
          <FileUploader
            onFileSelect={handleFileSelect}
            selectedFile={wizardState.selectedFile}
            isProcessing={wizardState.isProcessing}
            error={wizardState.error}
            onNext={handleProcessFile}
            onCancel={onCancel}
          />
        );

      case "mapping":
        if (!wizardState.csvData) {
          return null;
        }
        return (
          <FieldMapping
            csvData={wizardState.csvData}
            onMappingComplete={handleFieldMappingComplete}
            onPrevious={handlePrevious}
            onCancel={onCancel}
          />
        );

      case "preview":
        if (!wizardState.importResult || !wizardState.selectedFile) {
          return null;
        }
        return wizardState.importResult.success ? (
          <DataPreview
            importResult={wizardState.importResult}
            selectedFile={wizardState.selectedFile}
            onNext={handleNext}
            onPrevious={handlePrevious}
            onCancel={onCancel}
          />
        ) : (
          <ValidationResults importResult={wizardState.importResult} onRetry={handlePrevious} onCancel={onCancel} />
        );

      case "options":
        if (!wizardState.importResult) return null;
        return (
          <MergeOptions
            importResult={wizardState.importResult}
            currentDiscs={currentDiscs}
            mergeStrategy={wizardState.mergeStrategy}
            onMergeStrategyChange={(strategy) => updateState({ mergeStrategy: strategy })}
            onNext={handleCompleteImport}
            onPrevious={handlePrevious}
            onCancel={onCancel}
          />
        );

      default:
        return null;
    }
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className={cn("space-y-8", className)}>
      {/* Progress Indicator */}
      <ImportProgressIndicator currentStep={wizardState.currentStep} />

      {/* Import Progress Overlay */}
      {showProgress && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <ImportProgress
            progress={progress}
            isVisible={showProgress}
            error={progressError}
            onCancel={() => {
              hideProgress();
              updateState({ isProcessing: false });
            }}
          />
        </div>
      )}

      {/* Wizard Step Content */}
      {renderStepContent()}
    </div>
  );
}
