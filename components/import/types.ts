/**
 * Type definitions for Import Components
 *
 * This file contains all TypeScript interfaces and types used by the
 * import components in the Disc Golf Inventory Management System.
 */

import type { Disc } from "@/lib/types";
import type { ImportResult } from "@/lib/exportImport";

// ============================================================================
// ENUMS & CONSTANTS
// ============================================================================

/**
 * Import wizard step definitions
 */
export type ImportStep = "upload" | "mapping" | "preview" | "options" | "import";

/**
 * Merge strategy options
 */
export type MergeStrategy = "replace" | "merge" | "append";

/**
 * File format types
 */
export type ImportFileFormat = "json" | "csv";

// ============================================================================
// COMPONENT PROPS
// ============================================================================

/**
 * Import wizard state
 */
export interface ImportWizardState {
  currentStep: ImportStep;
  selectedFile: File | null;
  fileFormat: ImportFileFormat | null;
  csvData: CSVPreviewData | null;
  fieldMappings: Record<string, string> | null;
  importResult: ImportResult | null;
  mergeStrategy: MergeStrategy;
  isProcessing: boolean;
  error: string | null;
}

/**
 * CSV preview data
 */
export interface CSVPreviewData {
  headers: string[];
  rows: string[][];
  totalRows: number;
}

/**
 * Import wizard props
 */
export interface ImportWizardProps {
  currentDiscs: Disc[];
  onImportComplete: (discs: Disc[], strategy: MergeStrategy) => Promise<void>;
  onCancel: () => void;
  className?: string;
}

/**
 * File uploader props
 */
export interface FileUploaderProps {
  onFileSelect: (file: File) => void;
  selectedFile: File | null;
  isProcessing: boolean;
  error: string | null;
  onNext: () => void;
  onCancel: () => void;
  className?: string;
}

/**
 * Data preview props
 */
export interface DataPreviewProps {
  importResult: ImportResult;
  selectedFile: File;
  onNext: () => void;
  onPrevious: () => void;
  onCancel: () => void;
  className?: string;
}

/**
 * Validation results props
 */
export interface ValidationResultsProps {
  importResult: ImportResult;
  onRetry: () => void;
  onCancel: () => void;
  className?: string;
}

/**
 * Field mapping props
 */
export interface FieldMappingProps {
  csvData: CSVPreviewData;
  onMappingComplete: (mappings: Record<string, string>) => void;
  onPrevious: () => void;
  onCancel: () => void;
  className?: string;
}

/**
 * Merge options props
 */
export interface MergeOptionsProps {
  importResult: ImportResult;
  currentDiscs: Disc[];
  mergeStrategy: MergeStrategy;
  onMergeStrategyChange: (strategy: MergeStrategy) => void;
  onNext: () => void;
  onPrevious: () => void;
  onCancel: () => void;
  className?: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Import preview data for display
 */
export interface ImportPreviewData {
  totalDiscs: number;
  validDiscs: number;
  invalidDiscs: number;
  duplicates: number;
  newDiscs: number;
  sampleDiscs: Disc[];
}

/**
 * Merge conflict information
 */
export interface MergeConflict {
  existingDisc: Disc;
  importedDisc: Disc;
  conflictFields: string[];
}
