/**
 * ImportHistoryManager Component for Import History & Management
 *
 * Provides a comprehensive interface for viewing import history,
 * managing operations, and implementing undo/redo functionality.
 */

"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  History, 
  Undo, 
  Trash2, 
  Search,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  Globe,
  Clipboard,
  Type,
  Upload,
  AlertTriangle,
  Info,
  BarChart3
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  getImportHistory,
  getImportHistorySummary,
  getUndoRedoState,
  undoLastImport,
  clearImportHistory,
  deleteImportOperation,
  searchImportHistory,
  filterImportHistory,
  getImportStatsByPeriod,
  type ImportOperation,
  type ImportHistorySummary,
  type UndoRedoState,
} from "@/lib/importHistory";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Import history manager props
 */
export interface ImportHistoryManagerProps {
  onUndoComplete?: (operation: ImportOperation) => void;
  className?: string;
}

// ============================================================================
// HELPER COMPONENTS
// ============================================================================

/**
 * Operation status badge
 */
function OperationStatusBadge({ status }: { status: ImportOperation["status"] }) {
  const variants = {
    pending: { variant: "secondary" as const, icon: Clock, color: "text-yellow-600" },
    "in-progress": { variant: "default" as const, icon: Clock, color: "text-blue-600" },
    completed: { variant: "default" as const, icon: CheckCircle, color: "text-green-600" },
    failed: { variant: "destructive" as const, icon: XCircle, color: "text-red-600" },
    cancelled: { variant: "secondary" as const, icon: XCircle, color: "text-gray-600" },
  };

  const config = variants[status];
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className={config.color}>
      <Icon className="h-3 w-3 mr-1" />
      {status}
    </Badge>
  );
}

/**
 * Source type icon
 */
function SourceTypeIcon({ type }: { type: string }) {
  const icons = {
    file: FileText,
    url: Globe,
    clipboard: Clipboard,
    text: Type,
    batch: Upload,
  };

  const Icon = icons[type as keyof typeof icons] || FileText;
  return <Icon className="h-4 w-4" />;
}

/**
 * Operation row component
 */
interface OperationRowProps {
  operation: ImportOperation;
  onDelete: (id: string) => void;
  onUndo?: (operation: ImportOperation) => void;
  canUndo: boolean;
}

function OperationRow({ operation, onDelete, onUndo, canUndo }: OperationRowProps) {
  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <TableRow>
      <TableCell>
        <div className="flex items-center space-x-2">
          <SourceTypeIcon type={operation.source.type} />
          <div>
            <div className="font-medium text-sm">{operation.source.name}</div>
            <div className="text-xs text-muted-foreground">{operation.type}</div>
          </div>
        </div>
      </TableCell>
      <TableCell>
        <OperationStatusBadge status={operation.status} />
      </TableCell>
      <TableCell className="text-sm">
        {operation.status === "completed" && (
          <div>
            <div>{operation.result.newDiscs} new discs</div>
            <div className="text-xs text-muted-foreground">
              {operation.result.duplicates} duplicates
            </div>
          </div>
        )}
        {operation.status === "failed" && (
          <div className="text-red-600 text-xs">{operation.error}</div>
        )}
      </TableCell>
      <TableCell className="text-sm text-muted-foreground">
        <div>{formatDate(operation.timestamp)}</div>
        <div className="text-xs">{formatDuration(operation.metadata.duration)}</div>
      </TableCell>
      <TableCell>
        <div className="flex space-x-1">
          {canUndo && onUndo && operation.status === "completed" && operation.snapshot?.beforeData && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onUndo(operation)}
              title="Undo this import"
            >
              <Undo className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(operation.id)}
            title="Delete from history"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );
}

/**
 * History summary component
 */
function HistorySummary({ summary }: { summary: ImportHistorySummary }) {
  const stats = [
    { label: "Total Operations", value: summary.totalOperations, icon: History },
    { label: "Successful", value: summary.successfulOperations, icon: CheckCircle, color: "text-green-600" },
    { label: "Failed", value: summary.failedOperations, icon: XCircle, color: "text-red-600" },
    { label: "Discs Imported", value: summary.totalDiscsImported, icon: Download, color: "text-blue-600" },
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {stats.map((stat) => {
        const Icon = stat.icon;
        return (
          <Card key={stat.label}>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Icon className={cn("h-5 w-5", stat.color || "text-muted-foreground")} />
                <div>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}

/**
 * Statistics tab component
 */
function StatisticsTab() {
  const periods = [7, 30, 90];
  const stats = periods.map(days => getImportStatsByPeriod(days));

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {stats.map((stat) => (
          <Card key={stat.period}>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">{stat.period}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Operations</span>
                <span className="font-medium">{stat.operations}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Discs Imported</span>
                <span className="font-medium">{stat.discsImported}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Success Rate</span>
                <span className="font-medium">{stat.successRate}%</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * ImportHistoryManager component for managing import history
 */
export function ImportHistoryManager({ onUndoComplete, className }: ImportHistoryManagerProps) {
  const [history, setHistory] = React.useState<ImportOperation[]>([]);
  const [filteredHistory, setFilteredHistory] = React.useState<ImportOperation[]>([]);
  const [summary, setSummary] = React.useState<ImportHistorySummary | null>(null);
  const [undoRedoState, setUndoRedoState] = React.useState<UndoRedoState | null>(null);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [message, setMessage] = React.useState<{ type: "success" | "error"; text: string } | null>(null);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  React.useEffect(() => {
    loadData();
  }, []);

  React.useEffect(() => {
    if (searchQuery.trim()) {
      setFilteredHistory(searchImportHistory(searchQuery));
    } else {
      setFilteredHistory(history);
    }
  }, [searchQuery, history]);

  React.useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const loadData = () => {
    const importHistory = getImportHistory();
    const importSummary = getImportHistorySummary();
    const undoRedo = getUndoRedoState();

    setHistory(importHistory);
    setFilteredHistory(importHistory);
    setSummary(importSummary);
    setUndoRedoState(undoRedo);
  };

  const handleDeleteOperation = (operationId: string) => {
    if (!confirm("Are you sure you want to delete this operation from history?")) return;

    const success = deleteImportOperation(operationId);
    if (success) {
      loadData();
      setMessage({ type: "success", text: "Operation deleted from history" });
    } else {
      setMessage({ type: "error", text: "Failed to delete operation" });
    }
  };

  const handleUndoOperation = async (operation: ImportOperation) => {
    if (!confirm(`Are you sure you want to undo the import "${operation.source.name}"? This will restore your collection to its previous state.`)) {
      return;
    }

    try {
      const result = undoLastImport();
      if (result.success && result.operation) {
        loadData();
        setMessage({ type: "success", text: "Import operation undone successfully" });
        
        if (onUndoComplete) {
          onUndoComplete(result.operation);
        }
      } else {
        setMessage({ type: "error", text: result.error || "Failed to undo operation" });
      }
    } catch (error) {
      setMessage({ type: "error", text: "Failed to undo operation" });
    }
  };

  const handleClearHistory = () => {
    if (!confirm("Are you sure you want to clear all import history? This action cannot be undone.")) {
      return;
    }

    clearImportHistory();
    loadData();
    setMessage({ type: "success", text: "Import history cleared" });
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Import History</h2>
          <p className="text-muted-foreground">Track and manage your import operations</p>
        </div>
        <div className="flex space-x-2">
          {undoRedoState?.canUndo && (
            <Button
              variant="outline"
              onClick={() => undoRedoState.undoOperation && handleUndoOperation(undoRedoState.undoOperation)}
            >
              <Undo className="h-4 w-4 mr-2" />
              Undo Last Import
            </Button>
          )}
          <Button variant="outline" onClick={handleClearHistory}>
            <Trash2 className="h-4 w-4 mr-2" />
            Clear History
          </Button>
        </div>
      </div>

      {/* Message */}
      {message && (
        <Alert variant={message.type === "error" ? "destructive" : "default"}>
          {message.type === "success" ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <AlertTriangle className="h-4 w-4" />
          )}
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* Summary */}
      {summary && <HistorySummary summary={summary} />}

      {/* Tabs */}
      <Tabs defaultValue="history">
        <TabsList>
          <TabsTrigger value="history">
            <History className="h-4 w-4 mr-2" />
            History
          </TabsTrigger>
          <TabsTrigger value="statistics">
            <BarChart3 className="h-4 w-4 mr-2" />
            Statistics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="history" className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search import history..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* History Table */}
          {filteredHistory.length > 0 ? (
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Source</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Result</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredHistory.map((operation) => (
                    <OperationRow
                      key={operation.id}
                      operation={operation}
                      onDelete={handleDeleteOperation}
                      onUndo={handleUndoOperation}
                      canUndo={undoRedoState?.canUndo || false}
                    />
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <History className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">
                {searchQuery ? "No operations match your search." : "No import history found."}
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="statistics">
          <StatisticsTab />
        </TabsContent>
      </Tabs>
    </div>
  );
}
