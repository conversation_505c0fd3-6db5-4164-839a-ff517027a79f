/**
 * Enhanced Database Search Tab Component for Disc Golf Inventory Management System
 *
 * Extended search interface supporting both DiscIt API and static databases
 * (PDGA approved discs, community flight numbers, manufacturer collections).
 */

"use client";

import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { Search, Loader2, AlertCircle, Database, CheckCircle, X, Shield, Users, Building } from "lucide-react";
import { cn } from "@/lib/utils";

// Import both APIs
import {
  searchAndTransformDiscItAPI,
  getDiscItBrands,
  getDiscItCategories,
  getDiscItStabilityOptions,
  type DiscItSearchFilters,
} from "@/lib/discDatabaseAPI";

import {
  searchStaticDatabase,
  getStaticDatabaseProviders,
  getStaticDatabaseManufacturers,
  getStaticDatabaseCategories,
  type StaticDatabaseProvider,
  type StaticDatabaseFilters,
} from "@/lib/staticDatabaseAPI";

import type { Disc } from "@/lib/types";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Database provider types
 */
export type DatabaseProvider = "discit-api" | StaticDatabaseProvider;

/**
 * Props for the Enhanced Database Search Tab component
 */
export interface EnhancedDatabaseSearchTabProps {
  onDiscSelect: (discs: Disc[]) => void;
  isLoading: boolean;
  className?: string;
}

/**
 * Search state interface
 */
interface SearchState {
  provider: DatabaseProvider;
  query: string;
  filters: DiscItSearchFilters | StaticDatabaseFilters;
  results: Disc[];
  selectedDiscs: Set<string>;
  isSearching: boolean;
  error: string | null;
  hasSearched: boolean;
}

/**
 * Filter options interface
 */
interface FilterOptions {
  brands: string[];
  categories: string[];
  stabilityOptions: string[];
}

// ============================================================================
// PROVIDER CONFIGURATIONS
// ============================================================================

const PROVIDER_CONFIG = {
  "discit-api": {
    name: "DiscIt API",
    description: "Live disc database with comprehensive flight data",
    icon: Database,
    color: "blue",
  },
  pdga: {
    name: "PDGA Approved",
    description: "Official PDGA approved discs for tournament play",
    icon: Shield,
    color: "green",
  },
  community: {
    name: "Community Database",
    description: "Community-curated flight numbers and reviews",
    icon: Users,
    color: "purple",
  },
  manufacturer: {
    name: "Manufacturer Collections",
    description: "Comprehensive manufacturer-specific disc data",
    icon: Building,
    color: "orange",
  },
} as const;

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * Enhanced Database Search Tab component
 */
export function EnhancedDatabaseSearchTab({ onDiscSelect, isLoading, className }: EnhancedDatabaseSearchTabProps) {
  // ============================================================================
  // STATE
  // ============================================================================

  const [searchState, setSearchState] = React.useState<SearchState>({
    provider: "discit-api",
    query: "",
    filters: {},
    results: [],
    selectedDiscs: new Set(),
    isSearching: false,
    error: null,
    hasSearched: false,
  });

  const [filterOptions, setFilterOptions] = React.useState<FilterOptions>({
    brands: [],
    categories: [],
    stabilityOptions: [],
  });

  // ============================================================================
  // EFFECTS
  // ============================================================================

  // Load filter options when provider changes
  React.useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        let brandsResult, categoriesResult, stabilityResult;

        if (searchState.provider === "discit-api") {
          [brandsResult, categoriesResult, stabilityResult] = await Promise.all([
            getDiscItBrands(),
            getDiscItCategories(),
            getDiscItStabilityOptions(),
          ]);
        } else {
          // For static databases
          [brandsResult, categoriesResult] = await Promise.all([
            getStaticDatabaseManufacturers(searchState.provider as StaticDatabaseProvider),
            getStaticDatabaseCategories(searchState.provider as StaticDatabaseProvider),
          ]);

          // Static stability options
          stabilityResult = {
            success: true,
            data: ["Overstable", "Stable", "Understable", "Very Overstable", "Very Understable"],
          };
        }

        setFilterOptions({
          brands: brandsResult.data || [],
          categories: categoriesResult.data || [],
          stabilityOptions: stabilityResult.data || [],
        });
      } catch (error) {
        console.error("Failed to load filter options:", error);
        setFilterOptions({ brands: [], categories: [], stabilityOptions: [] });
      }
    };

    loadFilterOptions();
  }, [searchState.provider]);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  /**
   * Handle provider change
   */
  const handleProviderChange = (provider: DatabaseProvider) => {
    setSearchState((prev) => ({
      ...prev,
      provider,
      filters: {},
      results: [],
      selectedDiscs: new Set(),
      hasSearched: false,
      error: null,
    }));
  };

  /**
   * Handle search execution
   */
  const handleSearch = async () => {
    setSearchState((prev) => ({
      ...prev,
      isSearching: true,
      error: null,
    }));

    try {
      let result;

      if (searchState.provider === "discit-api") {
        const searchFilters: DiscItSearchFilters = {
          ...(searchState.filters as DiscItSearchFilters),
          limit: 50,
        };
        result = await searchAndTransformDiscItAPI(searchFilters);
      } else {
        const searchFilters: StaticDatabaseFilters = {
          ...(searchState.filters as StaticDatabaseFilters),
          provider: searchState.provider as StaticDatabaseProvider,
          limit: 50,
        };
        result = await searchStaticDatabase(searchFilters);
      }

      if (result.success && result.data) {
        setSearchState((prev) => ({
          ...prev,
          results: result.data || [],
          hasSearched: true,
          isSearching: false,
          selectedDiscs: new Set(),
        }));
      } else {
        setSearchState((prev) => ({
          ...prev,
          error: result.error || "Search failed",
          isSearching: false,
          hasSearched: true,
        }));
      }
    } catch (error) {
      setSearchState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "Search failed",
        isSearching: false,
        hasSearched: true,
      }));
    }
  };

  /**
   * Handle disc selection toggle
   */
  const handleDiscToggle = (discId: string) => {
    setSearchState((prev) => {
      const newSelected = new Set(prev.selectedDiscs);
      if (newSelected.has(discId)) {
        newSelected.delete(discId);
      } else {
        newSelected.add(discId);
      }
      return { ...prev, selectedDiscs: newSelected };
    });
  };

  /**
   * Handle select all toggle
   */
  const handleSelectAll = () => {
    setSearchState((prev) => {
      const allSelected = prev.results.length === prev.selectedDiscs.size;
      return {
        ...prev,
        selectedDiscs: allSelected ? new Set() : new Set(prev.results.map((disc) => disc.id)),
      };
    });
  };

  /**
   * Handle import selected discs
   */
  const handleImportSelected = () => {
    const selectedDiscs = searchState.results.filter((disc) => searchState.selectedDiscs.has(disc.id));
    onDiscSelect(selectedDiscs);
  };

  /**
   * Handle filter change
   */
  const handleFilterChange = (key: string, value: string) => {
    setSearchState((prev) => ({
      ...prev,
      filters: {
        ...prev.filters,
        [key]: value || undefined,
      },
    }));
  };

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const selectedCount = searchState.selectedDiscs.size;
  const allSelected = searchState.results.length > 0 && searchState.results.length === selectedCount;
  const someSelected = selectedCount > 0 && selectedCount < searchState.results.length;

  const currentProviderConfig = PROVIDER_CONFIG[searchState.provider];

  return (
    <div className={cn("space-y-6", className)}>
      {/* Provider Selection */}
      <div className="space-y-4">
        <div>
          <Label className="text-base font-medium">Database Provider</Label>
          <p className="text-sm text-muted-foreground">Choose which database to search</p>
        </div>

        <Tabs value={searchState.provider} onValueChange={handleProviderChange}>
          <TabsList className="grid w-full grid-cols-4">
            {Object.entries(PROVIDER_CONFIG).map(([key, config]) => {
              const Icon = config.icon;
              return (
                <TabsTrigger key={key} value={key} className="flex items-center gap-2">
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{config.name}</span>
                </TabsTrigger>
              );
            })}
          </TabsList>
        </Tabs>

        {/* Provider Description */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <currentProviderConfig.icon className="h-5 w-5" />
              <CardTitle className="text-lg">{currentProviderConfig.name}</CardTitle>
            </div>
            <CardDescription>{currentProviderConfig.description}</CardDescription>
          </CardHeader>
        </Card>
      </div>

      {/* Search Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Discs
          </CardTitle>
          <CardDescription>Search for discs to import into your collection</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Query */}
          <div className="space-y-2">
            <Label htmlFor="search-query">Search Query</Label>
            <Input
              id="search-query"
              placeholder="Search by disc name or manufacturer..."
              value={searchState.query}
              onChange={(e) => setSearchState((prev) => ({ ...prev, query: e.target.value }))}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
            />
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Manufacturer Filter */}
            <div className="space-y-2">
              <Label>Manufacturer</Label>
              <Select
                value={(searchState.filters as any).manufacturer || ""}
                onValueChange={(value) => handleFilterChange("manufacturer", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Any manufacturer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any manufacturer</SelectItem>
                  {filterOptions.brands.map((brand) => (
                    <SelectItem key={brand} value={brand}>
                      {brand}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Category Filter */}
            <div className="space-y-2">
              <Label>Category</Label>
              <Select
                value={(searchState.filters as any).category || ""}
                onValueChange={(value) => handleFilterChange("category", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Any category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any category</SelectItem>
                  {filterOptions.categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Stability Filter */}
            <div className="space-y-2">
              <Label>Stability</Label>
              <Select
                value={(searchState.filters as any).stability || ""}
                onValueChange={(value) => handleFilterChange("stability", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Any stability" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any stability</SelectItem>
                  {filterOptions.stabilityOptions.map((stability) => (
                    <SelectItem key={stability} value={stability}>
                      {stability}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Search Button */}
          <Button onClick={handleSearch} disabled={searchState.isSearching || isLoading} className="w-full">
            {searchState.isSearching ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Searching...
              </>
            ) : (
              <>
                <Search className="mr-2 h-4 w-4" />
                Search Discs
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Error Display */}
      {searchState.error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{searchState.error}</AlertDescription>
        </Alert>
      )}

      {/* Results */}
      {searchState.hasSearched && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Search Results</CardTitle>
                <CardDescription>
                  Found {searchState.results.length} discs
                  {selectedCount > 0 && ` • ${selectedCount} selected`}
                </CardDescription>
              </div>
              {searchState.results.length > 0 && (
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={handleSelectAll}>
                    {allSelected ? "Deselect All" : "Select All"}
                  </Button>
                  {selectedCount > 0 && (
                    <Button onClick={handleImportSelected} disabled={isLoading} size="sm">
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Import Selected ({selectedCount})
                    </Button>
                  )}
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {searchState.results.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No discs found. Try adjusting your search criteria.
              </div>
            ) : (
              <div className="grid gap-3">
                {searchState.results.map((disc) => (
                  <DiscResultCard
                    key={disc.id}
                    disc={disc}
                    isSelected={searchState.selectedDiscs.has(disc.id)}
                    onToggle={() => handleDiscToggle(disc.id)}
                  />
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// ============================================================================
// DISC RESULT CARD COMPONENT
// ============================================================================

interface DiscResultCardProps {
  disc: Disc;
  isSelected: boolean;
  onToggle: () => void;
}

function DiscResultCard({ disc, isSelected, onToggle }: DiscResultCardProps) {
  return (
    <div
      className={cn(
        "flex items-center gap-4 p-4 border rounded-lg cursor-pointer transition-colors",
        isSelected ? "border-primary bg-primary/5" : "border-border hover:border-primary/50"
      )}
      onClick={onToggle}
    >
      {/* Selection Indicator */}
      <div className="flex-shrink-0">
        {isSelected ? (
          <CheckCircle className="h-5 w-5 text-primary" />
        ) : (
          <div className="h-5 w-5 border-2 border-muted-foreground rounded-full" />
        )}
      </div>

      {/* Disc Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h4 className="font-medium truncate">
            {disc.manufacturer} {disc.mold}
          </h4>
          <Badge variant="secondary" className="text-xs">
            {disc.plasticType}
          </Badge>
        </div>

        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>
            {disc.flightNumbers.speed}|{disc.flightNumbers.glide}|{disc.flightNumbers.turn}|{disc.flightNumbers.fade}
          </span>
          <span>{disc.weight}g</span>
          <span className="truncate">{disc.notes}</span>
        </div>
      </div>
    </div>
  );
}
