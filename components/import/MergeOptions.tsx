/**
 * MergeOptions Component for Import Wizard
 *
 * Allows users to choose how to handle imported data and existing collection.
 */

"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  Download, 
  Plus, 
  RefreshCw, 
  AlertTriangle, 
  Info,
  Loader2 
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { MergeOptionsProps, MergeStrategy } from "./types";

// ============================================================================
// MERGE STRATEGY CONFIGURATIONS
// ============================================================================

interface StrategyConfig {
  value: MergeStrategy;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  warning?: string;
  recommended?: boolean;
}

const MERGE_STRATEGIES: StrategyConfig[] = [
  {
    value: "merge",
    title: "Merge with Existing",
    description: "Add new discs and update existing ones. Duplicates will be updated with imported data.",
    icon: Plus,
    recommended: true,
  },
  {
    value: "append",
    title: "Append Only",
    description: "Add only new discs. Skip any duplicates to preserve existing data.",
    icon: RefreshCw,
  },
  {
    value: "replace",
    title: "Replace Collection",
    description: "Replace your entire collection with the imported data.",
    icon: Download,
    warning: "This will permanently delete your current collection!",
  },
];

// ============================================================================
// HELPER COMPONENTS
// ============================================================================

/**
 * Strategy option card
 */
interface StrategyCardProps {
  strategy: StrategyConfig;
  isSelected: boolean;
  onSelect: (value: MergeStrategy) => void;
  disabled?: boolean;
}

function StrategyCard({ strategy, isSelected, onSelect, disabled }: StrategyCardProps) {
  const Icon = strategy.icon;

  return (
    <div
      className={cn(
        "relative border rounded-lg p-4 cursor-pointer transition-all",
        isSelected && "border-primary bg-primary/5",
        !isSelected && "border-muted hover:border-primary/50",
        disabled && "opacity-50 cursor-not-allowed"
      )}
      onClick={() => !disabled && onSelect(strategy.value)}
    >
      <div className="flex items-start space-x-3">
        <RadioGroupItem
          value={strategy.value}
          id={strategy.value}
          disabled={disabled}
          className="mt-1"
        />
        <div className="flex-1 space-y-2">
          <div className="flex items-center space-x-2">
            <Icon className="h-4 w-4" />
            <Label htmlFor={strategy.value} className="font-medium cursor-pointer">
              {strategy.title}
            </Label>
            {strategy.recommended && (
              <Badge variant="secondary" className="text-xs">
                Recommended
              </Badge>
            )}
          </div>
          <p className="text-sm text-muted-foreground">{strategy.description}</p>
          {strategy.warning && (
            <Alert variant="destructive" className="mt-2">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-xs">{strategy.warning}</AlertDescription>
            </Alert>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Import impact summary
 */
interface ImportImpactProps {
  strategy: MergeStrategy;
  importResult: any;
  currentDiscsCount: number;
}

function ImportImpact({ strategy, importResult, currentDiscsCount }: ImportImpactProps) {
  const { summary, data } = importResult;
  const importCount = data?.length || 0;

  const getImpactText = () => {
    switch (strategy) {
      case "replace":
        return {
          action: "Replace entire collection",
          result: `${currentDiscsCount} discs will be removed, ${importCount} discs will be added`,
          total: importCount,
        };
      case "merge":
        return {
          action: "Merge with existing collection",
          result: `${summary?.newDiscs || 0} new discs added, ${summary?.duplicates || 0} existing discs updated`,
          total: currentDiscsCount - (summary?.duplicates || 0) + importCount,
        };
      case "append":
        return {
          action: "Append new discs only",
          result: `${summary?.newDiscs || 0} new discs added, ${summary?.duplicates || 0} duplicates skipped`,
          total: currentDiscsCount + (summary?.newDiscs || 0),
        };
      default:
        return { action: "", result: "", total: currentDiscsCount };
    }
  };

  const impact = getImpactText();

  return (
    <Alert>
      <Info className="h-4 w-4" />
      <AlertDescription>
        <div className="space-y-1">
          <div className="font-medium">{impact.action}</div>
          <div className="text-sm">{impact.result}</div>
          <div className="text-sm">
            <strong>Final collection size:</strong> {impact.total} discs
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * MergeOptions component for choosing import strategy
 */
export function MergeOptions({
  importResult,
  currentDiscs,
  mergeStrategy,
  onMergeStrategyChange,
  onNext,
  onPrevious,
  onCancel,
  className,
}: MergeOptionsProps) {
  const [isImporting, setIsImporting] = React.useState(false);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleImport = async () => {
    setIsImporting(true);
    try {
      await onNext();
    } finally {
      setIsImporting(false);
    }
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle>Import Options</CardTitle>
          <CardDescription>
            Choose how to handle the imported data with your existing collection.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Strategy Selection */}
          <RadioGroup value={mergeStrategy} onValueChange={onMergeStrategyChange}>
            <div className="space-y-3">
              {MERGE_STRATEGIES.map((strategy) => (
                <StrategyCard
                  key={strategy.value}
                  strategy={strategy}
                  isSelected={mergeStrategy === strategy.value}
                  onSelect={onMergeStrategyChange}
                  disabled={isImporting}
                />
              ))}
            </div>
          </RadioGroup>

          {/* Import Impact Summary */}
          <ImportImpact
            strategy={mergeStrategy}
            importResult={importResult}
            currentDiscsCount={currentDiscs.length}
          />

          {/* Additional Warnings */}
          {mergeStrategy === "replace" && currentDiscs.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Warning:</strong> This action cannot be undone. Consider exporting your current
                collection as a backup before proceeding.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrevious} disabled={isImporting}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Preview
        </Button>
        <div className="space-x-2">
          <Button variant="outline" onClick={onCancel} disabled={isImporting}>
            Cancel
          </Button>
          <Button onClick={handleImport} disabled={isImporting} className="min-w-[140px]">
            {isImporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Importing...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Import Collection
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
