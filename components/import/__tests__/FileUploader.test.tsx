/**
 * FileUploader Component Tests
 *
 * Unit tests for the FileUploader component covering file selection,
 * drag-and-drop functionality, validation, and error handling.
 */

import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { FileUploader } from "../FileUploader";

describe("FileUploader", () => {
  const mockOnFileSelect = vi.fn();
  const mockOnNext = vi.fn();
  const mockOnCancel = vi.fn();

  const defaultProps = {
    onFileSelect: mockOnFileSelect,
    selectedFile: null,
    isProcessing: false,
    error: null,
    onNext: mockOnNext,
    onCancel: mockOnCancel,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Initial Render", () => {
    it("renders upload area with instructions", () => {
      render(<FileUploader {...defaultProps} />);
      
      expect(screen.getByText("Upload Collection Data")).toBeInTheDocument();
      expect(screen.getByText("Drop your file here or click to browse")).toBeInTheDocument();
      expect(screen.getByText("Supports JSON and CSV files up to 10MB")).toBeInTheDocument();
    });

    it("shows format information", () => {
      render(<FileUploader {...defaultProps} />);
      
      expect(screen.getByText("JSON Format")).toBeInTheDocument();
      expect(screen.getByText("CSV Format")).toBeInTheDocument();
      expect(screen.getByText(/full data support including metadata/i)).toBeInTheDocument();
      expect(screen.getByText(/spreadsheet-compatible format/i)).toBeInTheDocument();
    });

    it("disables next button when no file selected", () => {
      render(<FileUploader {...defaultProps} />);
      
      const nextButton = screen.getByRole("button", { name: /next: preview data/i });
      expect(nextButton).toBeDisabled();
    });
  });

  describe("File Selection", () => {
    it("calls onFileSelect when file is selected via input", async () => {
      render(<FileUploader {...defaultProps} />);
      
      const file = new File(['{"test": "data"}'], "test.json", { type: "application/json" });
      const fileInput = screen.getByRole("button", { name: /drop your file here or click to browse/i })
        .querySelector("input") as HTMLInputElement;
      
      await userEvent.upload(fileInput, file);
      
      expect(mockOnFileSelect).toHaveBeenCalledWith(file);
    });

    it("shows selected file information", () => {
      const file = new File(['{"test": "data"}'], "test.json", { type: "application/json" });
      
      render(<FileUploader {...defaultProps} selectedFile={file} />);
      
      expect(screen.getByText("test.json")).toBeInTheDocument();
      expect(screen.getByText(/KB/)).toBeInTheDocument();
    });

    it("enables next button when valid file is selected", () => {
      const file = new File(['{"test": "data"}'], "test.json", { type: "application/json" });
      
      render(<FileUploader {...defaultProps} selectedFile={file} />);
      
      const nextButton = screen.getByRole("button", { name: /next: preview data/i });
      expect(nextButton).not.toBeDisabled();
    });
  });

  describe("Drag and Drop", () => {
    it("handles drag over event", () => {
      render(<FileUploader {...defaultProps} />);
      
      const dropZone = screen.getByRole("button", { name: /drop your file here or click to browse/i });
      
      fireEvent.dragOver(dropZone, {
        dataTransfer: { files: [] },
      });
      
      // Should add active styling (tested via class changes)
      expect(dropZone).toHaveClass("cursor-pointer");
    });

    it("handles file drop", () => {
      render(<FileUploader {...defaultProps} />);
      
      const file = new File(['{"test": "data"}'], "test.json", { type: "application/json" });
      const dropZone = screen.getByRole("button", { name: /drop your file here or click to browse/i });
      
      fireEvent.drop(dropZone, {
        dataTransfer: { files: [file] },
      });
      
      expect(mockOnFileSelect).toHaveBeenCalledWith(file);
    });

    it("handles drag leave event", () => {
      render(<FileUploader {...defaultProps} />);
      
      const dropZone = screen.getByRole("button", { name: /drop your file here or click to browse/i });
      
      fireEvent.dragLeave(dropZone);
      
      // Should remove active styling
      expect(dropZone).toHaveClass("cursor-pointer");
    });
  });

  describe("File Validation", () => {
    it("shows error for invalid file type", () => {
      const file = new File(["test content"], "test.txt", { type: "text/plain" });
      
      render(<FileUploader {...defaultProps} selectedFile={file} />);
      
      expect(screen.getByText(/please select a valid json or csv file/i)).toBeInTheDocument();
      
      const nextButton = screen.getByRole("button", { name: /next: preview data/i });
      expect(nextButton).toBeDisabled();
    });

    it("accepts JSON files", () => {
      const file = new File(['{"test": "data"}'], "test.json", { type: "application/json" });
      
      render(<FileUploader {...defaultProps} selectedFile={file} />);
      
      expect(screen.queryByText(/please select a valid json or csv file/i)).not.toBeInTheDocument();
      
      const nextButton = screen.getByRole("button", { name: /next: preview data/i });
      expect(nextButton).not.toBeDisabled();
    });

    it("accepts CSV files", () => {
      const file = new File(["header1,header2\nvalue1,value2"], "test.csv", { type: "text/csv" });
      
      render(<FileUploader {...defaultProps} selectedFile={file} />);
      
      expect(screen.queryByText(/please select a valid json or csv file/i)).not.toBeInTheDocument();
      
      const nextButton = screen.getByRole("button", { name: /next: preview data/i });
      expect(nextButton).not.toBeDisabled();
    });
  });

  describe("Error Display", () => {
    it("shows error message when error prop is provided", () => {
      render(<FileUploader {...defaultProps} error="Test error message" />);
      
      expect(screen.getByText("Test error message")).toBeInTheDocument();
    });

    it("hides error when no error prop", () => {
      render(<FileUploader {...defaultProps} />);
      
      expect(screen.queryByRole("alert")).not.toBeInTheDocument();
    });
  });

  describe("Processing State", () => {
    it("shows processing state when isProcessing is true", () => {
      render(<FileUploader {...defaultProps} isProcessing={true} />);
      
      expect(screen.getByText("Processing...")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /processing/i })).toBeDisabled();
    });

    it("disables buttons during processing", () => {
      const file = new File(['{"test": "data"}'], "test.json", { type: "application/json" });
      
      render(<FileUploader {...defaultProps} selectedFile={file} isProcessing={true} />);
      
      const nextButton = screen.getByRole("button", { name: /processing/i });
      const cancelButton = screen.getByRole("button", { name: /cancel/i });
      
      expect(nextButton).toBeDisabled();
      expect(cancelButton).not.toBeDisabled(); // Cancel should still work
    });
  });

  describe("Button Actions", () => {
    it("calls onNext when next button is clicked", async () => {
      const file = new File(['{"test": "data"}'], "test.json", { type: "application/json" });
      
      render(<FileUploader {...defaultProps} selectedFile={file} />);
      
      const nextButton = screen.getByRole("button", { name: /next: preview data/i });
      await userEvent.click(nextButton);
      
      expect(mockOnNext).toHaveBeenCalled();
    });

    it("calls onCancel when cancel button is clicked", async () => {
      render(<FileUploader {...defaultProps} />);
      
      const cancelButton = screen.getByRole("button", { name: /cancel/i });
      await userEvent.click(cancelButton);
      
      expect(mockOnCancel).toHaveBeenCalled();
    });
  });

  describe("Accessibility", () => {
    it("has proper ARIA labels", () => {
      render(<FileUploader {...defaultProps} />);
      
      const fileInput = screen.getByRole("button", { name: /drop your file here or click to browse/i })
        .querySelector("input");
      
      expect(fileInput).toHaveAttribute("accept", ".json,.csv");
    });

    it("shows proper error alerts", () => {
      render(<FileUploader {...defaultProps} error="Test error" />);
      
      const alert = screen.getByRole("alert");
      expect(alert).toBeInTheDocument();
      expect(alert).toHaveTextContent("Test error");
    });
  });
});
