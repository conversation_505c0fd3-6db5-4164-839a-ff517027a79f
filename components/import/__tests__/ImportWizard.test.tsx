/**
 * ImportWizard Component Tests
 *
 * Comprehensive unit tests for the ImportWizard component covering
 * all wizard steps, state management, and user interactions.
 */

import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { ImportWizard } from "../ImportWizard";
import { importFromFile } from "@/lib/exportImport";
import type { Disc } from "@/lib/types";
import { DiscCondition, Location } from "@/lib/types";

// Mock the import utility
vi.mock("@/lib/exportImport", () => ({
  importFromFile: vi.fn(),
}));

// Mock file reading
const mockFileReader = {
  readAsText: vi.fn(),
  result: "",
  onload: null as any,
  onerror: null as any,
};

Object.defineProperty(global, "FileReader", {
  writable: true,
  value: vi.fn(() => mockFileReader),
});

// Test data
const mockDisc: Disc = {
  id: "test-disc-1",
  manufacturer: "Innova",
  mold: "Destroyer",
  plasticType: "Champion",
  weight: 175,
  condition: DiscCondition.NEW,
  flightNumbers: { speed: 12, glide: 5, turn: -1, fade: 3 },
  color: "Blue",
  currentLocation: Location.BAG,
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-01"),
};

const mockImportResult = {
  success: true,
  data: [mockDisc],
  summary: {
    totalImported: 1,
    totalSkipped: 0,
    totalErrors: 0,
    newDiscs: 1,
    duplicates: 0,
    replacements: 0,
  },
};

describe("ImportWizard", () => {
  const mockOnImportComplete = vi.fn();
  const mockOnCancel = vi.fn();
  const currentDiscs: Disc[] = [];

  beforeEach(() => {
    vi.clearAllMocks();
    mockFileReader.readAsText.mockClear();
  });

  const defaultProps = {
    currentDiscs,
    onImportComplete: mockOnImportComplete,
    onCancel: mockOnCancel,
  };

  describe("Initial State", () => {
    it("renders upload step initially", () => {
      render(<ImportWizard {...defaultProps} />);
      
      expect(screen.getByText("Upload Collection Data")).toBeInTheDocument();
      expect(screen.getByText("Drop your file here or click to browse")).toBeInTheDocument();
    });

    it("shows progress indicator with upload step active", () => {
      render(<ImportWizard {...defaultProps} />);
      
      const uploadStep = screen.getByText("Upload File");
      expect(uploadStep).toBeInTheDocument();
      
      // Check that upload step is active (has primary styling)
      const uploadIcon = uploadStep.closest("div")?.querySelector("div");
      expect(uploadIcon).toHaveClass("border-primary", "bg-primary");
    });

    it("disables next button when no file is selected", () => {
      render(<ImportWizard {...defaultProps} />);
      
      const nextButton = screen.getByRole("button", { name: /next: preview data/i });
      expect(nextButton).toBeDisabled();
    });
  });

  describe("File Upload", () => {
    it("enables next button when valid file is selected", async () => {
      render(<ImportWizard {...defaultProps} />);
      
      const file = new File(['{"test": "data"}'], "test.json", { type: "application/json" });
      const fileInput = screen.getByRole("button", { name: /drop your file here or click to browse/i });
      
      await userEvent.upload(fileInput.querySelector("input")!, file);
      
      await waitFor(() => {
        const nextButton = screen.getByRole("button", { name: /next: preview data/i });
        expect(nextButton).not.toBeDisabled();
      });
    });

    it("shows error for invalid file type", async () => {
      render(<ImportWizard {...defaultProps} />);
      
      const file = new File(["test content"], "test.txt", { type: "text/plain" });
      const fileInput = screen.getByRole("button", { name: /drop your file here or click to browse/i });
      
      await userEvent.upload(fileInput.querySelector("input")!, file);
      
      expect(screen.getByText(/please select a valid json or csv file/i)).toBeInTheDocument();
    });

    it("processes file and moves to preview step on success", async () => {
      const mockImportFromFile = vi.mocked(importFromFile);
      mockImportFromFile.mockResolvedValue(mockImportResult);
      
      render(<ImportWizard {...defaultProps} />);
      
      const file = new File(['{"test": "data"}'], "test.json", { type: "application/json" });
      const fileInput = screen.getByRole("button", { name: /drop your file here or click to browse/i });
      
      await userEvent.upload(fileInput.querySelector("input")!, file);
      
      const nextButton = screen.getByRole("button", { name: /next: preview data/i });
      await userEvent.click(nextButton);
      
      await waitFor(() => {
        expect(screen.getByText("Data Preview")).toBeInTheDocument();
      });
    });
  });

  describe("Data Preview Step", () => {
    beforeEach(async () => {
      const mockImportFromFile = vi.mocked(importFromFile);
      mockImportFromFile.mockResolvedValue(mockImportResult);
      
      render(<ImportWizard {...defaultProps} />);
      
      const file = new File(['{"test": "data"}'], "test.json", { type: "application/json" });
      const fileInput = screen.getByRole("button", { name: /drop your file here or click to browse/i });
      
      await userEvent.upload(fileInput.querySelector("input")!, file);
      
      const nextButton = screen.getByRole("button", { name: /next: preview data/i });
      await userEvent.click(nextButton);
      
      await waitFor(() => {
        expect(screen.getByText("Data Preview")).toBeInTheDocument();
      });
    });

    it("displays import statistics", () => {
      expect(screen.getByText("1")).toBeInTheDocument(); // Valid discs
      expect(screen.getByText("Valid Discs")).toBeInTheDocument();
    });

    it("shows data preview table", () => {
      expect(screen.getByText("Innova")).toBeInTheDocument();
      expect(screen.getByText("Destroyer")).toBeInTheDocument();
      expect(screen.getByText("Champion")).toBeInTheDocument();
    });

    it("allows navigation to next step", async () => {
      const nextButton = screen.getByRole("button", { name: /next: import options/i });
      await userEvent.click(nextButton);
      
      await waitFor(() => {
        expect(screen.getByText("Import Options")).toBeInTheDocument();
      });
    });

    it("allows navigation back to upload", async () => {
      const backButton = screen.getByRole("button", { name: /back to upload/i });
      await userEvent.click(backButton);
      
      await waitFor(() => {
        expect(screen.getByText("Upload Collection Data")).toBeInTheDocument();
      });
    });
  });

  describe("Merge Options Step", () => {
    beforeEach(async () => {
      const mockImportFromFile = vi.mocked(importFromFile);
      mockImportFromFile.mockResolvedValue(mockImportResult);
      
      render(<ImportWizard {...defaultProps} />);
      
      // Navigate to merge options step
      const file = new File(['{"test": "data"}'], "test.json", { type: "application/json" });
      const fileInput = screen.getByRole("button", { name: /drop your file here or click to browse/i });
      await userEvent.upload(fileInput.querySelector("input")!, file);
      
      const nextButton1 = screen.getByRole("button", { name: /next: preview data/i });
      await userEvent.click(nextButton1);
      
      await waitFor(() => {
        expect(screen.getByText("Data Preview")).toBeInTheDocument();
      });
      
      const nextButton2 = screen.getByRole("button", { name: /next: import options/i });
      await userEvent.click(nextButton2);
      
      await waitFor(() => {
        expect(screen.getByText("Import Options")).toBeInTheDocument();
      });
    });

    it("shows merge strategy options", () => {
      expect(screen.getByText("Merge with Existing")).toBeInTheDocument();
      expect(screen.getByText("Append Only")).toBeInTheDocument();
      expect(screen.getByText("Replace Collection")).toBeInTheDocument();
    });

    it("shows merge strategy as default selected", () => {
      const mergeOption = screen.getByLabelText("Merge with Existing");
      expect(mergeOption).toBeChecked();
    });

    it("allows changing merge strategy", async () => {
      const replaceOption = screen.getByLabelText("Replace Collection");
      await userEvent.click(replaceOption);
      
      expect(replaceOption).toBeChecked();
      expect(screen.getByText(/this will permanently delete your current collection/i)).toBeInTheDocument();
    });

    it("completes import when import button is clicked", async () => {
      const importButton = screen.getByRole("button", { name: /import collection/i });
      await userEvent.click(importButton);
      
      await waitFor(() => {
        expect(mockOnImportComplete).toHaveBeenCalledWith([mockDisc], "merge");
      });
    });
  });

  describe("Error Handling", () => {
    it("shows validation results for failed import", async () => {
      const mockImportFromFile = vi.mocked(importFromFile);
      mockImportFromFile.mockResolvedValue({
        success: false,
        error: "Validation failed",
        validationErrors: [
          { index: 0, field: "manufacturer", message: "Manufacturer is required" },
        ],
      });
      
      render(<ImportWizard {...defaultProps} />);
      
      const file = new File(['{"test": "data"}'], "test.json", { type: "application/json" });
      const fileInput = screen.getByRole("button", { name: /drop your file here or click to browse/i });
      
      await userEvent.upload(fileInput.querySelector("input")!, file);
      
      const nextButton = screen.getByRole("button", { name: /next: preview data/i });
      await userEvent.click(nextButton);
      
      await waitFor(() => {
        expect(screen.getByText("Import Failed")).toBeInTheDocument();
        expect(screen.getByText("Manufacturer is required")).toBeInTheDocument();
      });
    });
  });

  describe("Cancel Functionality", () => {
    it("calls onCancel when cancel button is clicked", async () => {
      render(<ImportWizard {...defaultProps} />);
      
      const cancelButton = screen.getByRole("button", { name: /cancel/i });
      await userEvent.click(cancelButton);
      
      expect(mockOnCancel).toHaveBeenCalled();
    });
  });
});
