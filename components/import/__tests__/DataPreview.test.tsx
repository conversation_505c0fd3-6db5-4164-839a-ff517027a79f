/**
 * DataPreview Component Tests
 *
 * Unit tests for the DataPreview component covering data display,
 * statistics, validation results, and navigation.
 */

import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { DataPreview } from "../DataPreview";
import type { Disc } from "@/lib/types";
import { DiscCondition, Location } from "@/lib/types";

// Test data
const mockDisc: Disc = {
  id: "test-disc-1",
  manufacturer: "Innova",
  mold: "Destroyer",
  plasticType: "Champion",
  weight: 175,
  condition: DiscCondition.NEW,
  flightNumbers: { speed: 12, glide: 5, turn: -1, fade: 3 },
  color: "Blue",
  currentLocation: Location.BAG,
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-01"),
};

const mockImportResult = {
  success: true,
  data: [mockDisc],
  summary: {
    totalImported: 1,
    totalSkipped: 0,
    totalErrors: 0,
    newDiscs: 1,
    duplicates: 0,
    replacements: 0,
  },
};

const mockFile = new File(['{"test": "data"}'], "test.json", { type: "application/json" });

describe("DataPreview", () => {
  const mockOnNext = vi.fn();
  const mockOnPrevious = vi.fn();
  const mockOnCancel = vi.fn();

  const defaultProps = {
    importResult: mockImportResult,
    selectedFile: mockFile,
    onNext: mockOnNext,
    onPrevious: mockOnPrevious,
    onCancel: mockOnCancel,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Initial Render", () => {
    it("renders data preview header", () => {
      render(<DataPreview {...defaultProps} />);
      
      expect(screen.getByText("Data Preview")).toBeInTheDocument();
      expect(screen.getByText(/review your imported data before proceeding/i)).toBeInTheDocument();
      expect(screen.getByText("test.json")).toBeInTheDocument();
    });

    it("displays import statistics", () => {
      render(<DataPreview {...defaultProps} />);
      
      expect(screen.getByText("1")).toBeInTheDocument(); // Valid discs count
      expect(screen.getByText("Valid Discs")).toBeInTheDocument();
      expect(screen.getByText("New Discs")).toBeInTheDocument();
      expect(screen.getByText("Duplicates")).toBeInTheDocument();
      expect(screen.getByText("Errors")).toBeInTheDocument();
    });

    it("shows success alert for valid data", () => {
      render(<DataPreview {...defaultProps} />);
      
      expect(screen.getByText(/data validation successful/i)).toBeInTheDocument();
      expect(screen.getByText(/1 disc\(s\) ready to import/i)).toBeInTheDocument();
    });
  });

  describe("Data Table", () => {
    it("renders data preview table with disc information", () => {
      render(<DataPreview {...defaultProps} />);
      
      // Check table headers
      expect(screen.getByText("Manufacturer")).toBeInTheDocument();
      expect(screen.getByText("Mold")).toBeInTheDocument();
      expect(screen.getByText("Plastic")).toBeInTheDocument();
      expect(screen.getByText("Weight")).toBeInTheDocument();
      expect(screen.getByText("Flight Numbers")).toBeInTheDocument();
      expect(screen.getByText("Condition")).toBeInTheDocument();
      
      // Check disc data
      expect(screen.getByText("Innova")).toBeInTheDocument();
      expect(screen.getByText("Destroyer")).toBeInTheDocument();
      expect(screen.getByText("Champion")).toBeInTheDocument();
      expect(screen.getByText("175g")).toBeInTheDocument();
      expect(screen.getByText("12|5|-1|3")).toBeInTheDocument();
      expect(screen.getByText("new")).toBeInTheDocument();
    });

    it("shows limited rows with count indicator", () => {
      const multipleDiscs = Array.from({ length: 15 }, (_, i) => ({
        ...mockDisc,
        id: `disc-${i}`,
        mold: `Disc${i}`,
      }));
      
      const importResultWithMultiple = {
        ...mockImportResult,
        data: multipleDiscs,
        summary: {
          ...mockImportResult.summary,
          totalImported: 15,
        },
      };
      
      render(<DataPreview {...defaultProps} importResult={importResultWithMultiple} />);
      
      expect(screen.getByText("Showing 10 of 15 discs")).toBeInTheDocument();
    });

    it("handles missing flight numbers gracefully", () => {
      const discWithoutFlight = {
        ...mockDisc,
        flightNumbers: undefined as any,
      };
      
      const importResultWithoutFlight = {
        ...mockImportResult,
        data: [discWithoutFlight],
      };
      
      render(<DataPreview {...defaultProps} importResult={importResultWithoutFlight} />);
      
      expect(screen.getByText("-")).toBeInTheDocument(); // Should show dash for missing flight numbers
    });
  });

  describe("Validation Errors", () => {
    it("shows error alert when validation errors exist", () => {
      const importResultWithErrors = {
        ...mockImportResult,
        validationErrors: [
          { index: 0, field: "manufacturer", message: "Manufacturer is required" },
        ],
      };
      
      render(<DataPreview {...defaultProps} importResult={importResultWithErrors} />);
      
      expect(screen.getByText(/1 validation error\(s\) found/i)).toBeInTheDocument();
    });

    it("displays validation error details", () => {
      const importResultWithErrors = {
        ...mockImportResult,
        validationErrors: [
          { index: 0, field: "manufacturer", message: "Manufacturer is required" },
          { index: 1, field: "weight", message: "Weight must be a number" },
        ],
      };
      
      render(<DataPreview {...defaultProps} importResult={importResultWithErrors} />);
      
      expect(screen.getByText("Validation Errors")).toBeInTheDocument();
      expect(screen.getByText("Row 1: Manufacturer is required")).toBeInTheDocument();
      expect(screen.getByText("Row 2: Weight must be a number")).toBeInTheDocument();
    });

    it("limits displayed errors and shows count", () => {
      const manyErrors = Array.from({ length: 15 }, (_, i) => ({
        index: i,
        field: "test",
        message: `Error ${i}`,
      }));
      
      const importResultWithManyErrors = {
        ...mockImportResult,
        validationErrors: manyErrors,
      };
      
      render(<DataPreview {...defaultProps} importResult={importResultWithManyErrors} />);
      
      expect(screen.getByText("... and 5 more errors")).toBeInTheDocument();
    });
  });

  describe("Warnings", () => {
    it("shows warning for duplicates", () => {
      const importResultWithDuplicates = {
        ...mockImportResult,
        summary: {
          ...mockImportResult.summary,
          duplicates: 2,
        },
      };
      
      render(<DataPreview {...defaultProps} importResult={importResultWithDuplicates} />);
      
      expect(screen.getByText(/2 duplicate disc\(s\) detected/i)).toBeInTheDocument();
    });
  });

  describe("Navigation", () => {
    it("calls onNext when next button is clicked", async () => {
      render(<DataPreview {...defaultProps} />);
      
      const nextButton = screen.getByRole("button", { name: /next: import options/i });
      await userEvent.click(nextButton);
      
      expect(mockOnNext).toHaveBeenCalled();
    });

    it("calls onPrevious when back button is clicked", async () => {
      render(<DataPreview {...defaultProps} />);
      
      const backButton = screen.getByRole("button", { name: /back to upload/i });
      await userEvent.click(backButton);
      
      expect(mockOnPrevious).toHaveBeenCalled();
    });

    it("calls onCancel when cancel button is clicked", async () => {
      render(<DataPreview {...defaultProps} />);
      
      const cancelButton = screen.getByRole("button", { name: /cancel/i });
      await userEvent.click(cancelButton);
      
      expect(mockOnCancel).toHaveBeenCalled();
    });

    it("disables next button when no valid data", () => {
      const importResultWithoutData = {
        ...mockImportResult,
        data: [],
      };
      
      render(<DataPreview {...defaultProps} importResult={importResultWithoutData} />);
      
      const nextButton = screen.getByRole("button", { name: /next: import options/i });
      expect(nextButton).toBeDisabled();
    });
  });

  describe("Edge Cases", () => {
    it("handles empty data gracefully", () => {
      const emptyImportResult = {
        success: true,
        data: [],
        summary: {
          totalImported: 0,
          totalSkipped: 0,
          totalErrors: 0,
          newDiscs: 0,
          duplicates: 0,
          replacements: 0,
        },
      };
      
      render(<DataPreview {...defaultProps} importResult={emptyImportResult} />);
      
      expect(screen.getByText("0")).toBeInTheDocument(); // Should show 0 for all stats
    });

    it("handles missing summary data", () => {
      const importResultWithoutSummary = {
        ...mockImportResult,
        summary: undefined,
      };
      
      render(<DataPreview {...defaultProps} importResult={importResultWithoutSummary} />);
      
      // Should not crash and show 0 for missing data
      expect(screen.getByText("Valid Discs")).toBeInTheDocument();
    });
  });
});
