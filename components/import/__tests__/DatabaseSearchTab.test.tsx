/**
 * Integration tests for DatabaseSearchTab component
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DatabaseSearchTab } from '../DatabaseSearchTab';
import type { Disc } from '@/lib/types';

// Mock the API module
vi.mock('@/lib/discDatabaseAPI', () => ({
  searchAndTransformDiscItAPI: vi.fn(),
  getDiscItBrands: vi.fn(),
  getDiscItCategories: vi.fn(),
  getDiscItStabilityOptions: vi.fn(),
}));

import {
  searchAndTransformDiscItAPI,
  getDiscItBrands,
  getDiscItCategories,
  getDiscItStabilityOptions,
} from '@/lib/discDatabaseAPI';

describe('DatabaseSearchTab Integration', () => {
  const mockOnDiscSelect = vi.fn();
  
  const mockDiscs: Disc[] = [
    {
      id: '1',
      manufacturer: 'Innova',
      mold: 'Destroyer',
      plasticType: 'Champion',
      weight: 175,
      condition: 'new' as any,
      flightNumbers: { speed: 12, glide: 5, turn: -1, fade: 3 },
      color: 'Red',
      currentLocation: 'home' as any,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '2',
      manufacturer: 'Discraft',
      mold: 'Buzzz',
      plasticType: 'ESP',
      weight: 177,
      condition: 'new' as any,
      flightNumbers: { speed: 5, glide: 4, turn: -1, fade: 1 },
      color: 'Blue',
      currentLocation: 'home' as any,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock filter options
    vi.mocked(getDiscItBrands).mockResolvedValue({
      success: true,
      data: ['Innova', 'Discraft', 'Dynamic Discs'],
    });
    
    vi.mocked(getDiscItCategories).mockResolvedValue({
      success: true,
      data: ['Putter', 'Midrange', 'Fairway Driver', 'Distance Driver'],
    });
    
    vi.mocked(getDiscItStabilityOptions).mockResolvedValue({
      success: true,
      data: ['Very Overstable', 'Overstable', 'Stable', 'Understable'],
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should render search interface with all filter options', async () => {
    render(<DatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

    // Check that the component renders
    expect(screen.getByText('DiscIt Database Search')).toBeInTheDocument();
    expect(screen.getByText('Search the DiscIt database for disc golf discs to import into your collection.')).toBeInTheDocument();

    // Wait for filter options to load
    await waitFor(() => {
      expect(screen.getByDisplayValue('All Brands')).toBeInTheDocument();
    });

    // Check filter dropdowns
    expect(screen.getByLabelText('Brand')).toBeInTheDocument();
    expect(screen.getByLabelText('Category')).toBeInTheDocument();
    expect(screen.getByLabelText('Speed')).toBeInTheDocument();
    expect(screen.getByLabelText('Stability')).toBeInTheDocument();

    // Check search button
    expect(screen.getByRole('button', { name: /search discs/i })).toBeInTheDocument();
  });

  it('should perform search and display results', async () => {
    vi.mocked(searchAndTransformDiscItAPI).mockResolvedValue({
      success: true,
      data: mockDiscs,
      total: 2,
    });

    render(<DatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /search discs/i })).toBeInTheDocument();
    });

    // Click search button
    const searchButton = screen.getByRole('button', { name: /search discs/i });
    fireEvent.click(searchButton);

    // Wait for search to complete and results to appear
    await waitFor(() => {
      expect(screen.getByText('Search Results')).toBeInTheDocument();
    });

    // Check that results are displayed
    expect(screen.getByText('Found 2 discs. Select discs to import.')).toBeInTheDocument();
    expect(screen.getByText('Destroyer')).toBeInTheDocument();
    expect(screen.getByText('Buzzz')).toBeInTheDocument();
    expect(screen.getByText('Innova')).toBeInTheDocument();
    expect(screen.getByText('Discraft')).toBeInTheDocument();

    // Check flight numbers are displayed
    expect(screen.getByText('Speed: 12')).toBeInTheDocument();
    expect(screen.getByText('Speed: 5')).toBeInTheDocument();
  });

  it('should handle disc selection and import', async () => {
    vi.mocked(searchAndTransformDiscItAPI).mockResolvedValue({
      success: true,
      data: mockDiscs,
      total: 2,
    });

    render(<DatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

    // Perform search
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /search discs/i })).toBeInTheDocument();
    });

    const searchButton = screen.getByRole('button', { name: /search discs/i });
    fireEvent.click(searchButton);

    // Wait for results
    await waitFor(() => {
      expect(screen.getByText('Destroyer')).toBeInTheDocument();
    });

    // Select first disc
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[0]); // First disc checkbox

    // Check that import button appears
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /import selected \(1\)/i })).toBeInTheDocument();
    });

    // Click import button
    const importButton = screen.getByRole('button', { name: /import selected \(1\)/i });
    fireEvent.click(importButton);

    // Verify onDiscSelect was called with selected disc
    expect(mockOnDiscSelect).toHaveBeenCalledWith([mockDiscs[0]]);
  });

  it('should handle select all functionality', async () => {
    vi.mocked(searchAndTransformDiscItAPI).mockResolvedValue({
      success: true,
      data: mockDiscs,
      total: 2,
    });

    render(<DatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

    // Perform search and wait for results
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /search discs/i })).toBeInTheDocument();
    });

    fireEvent.click(screen.getByRole('button', { name: /search discs/i }));

    await waitFor(() => {
      expect(screen.getByText('Destroyer')).toBeInTheDocument();
    });

    // Click select all
    const selectAllButton = screen.getByRole('button', { name: /select all/i });
    fireEvent.click(selectAllButton);

    // Check that import button shows all discs selected
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /import selected \(2\)/i })).toBeInTheDocument();
    });

    // Click import
    fireEvent.click(screen.getByRole('button', { name: /import selected \(2\)/i }));

    // Verify all discs were selected
    expect(mockOnDiscSelect).toHaveBeenCalledWith(mockDiscs);
  });

  it('should handle search errors gracefully', async () => {
    vi.mocked(searchAndTransformDiscItAPI).mockResolvedValue({
      success: false,
      error: 'Network error occurred',
    });

    render(<DatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

    // Perform search
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /search discs/i })).toBeInTheDocument();
    });

    fireEvent.click(screen.getByRole('button', { name: /search discs/i }));

    // Wait for error to appear
    await waitFor(() => {
      expect(screen.getByText('Network error occurred')).toBeInTheDocument();
    });

    // Check that no results section appears
    expect(screen.queryByText('Search Results')).not.toBeInTheDocument();
  });

  it('should handle empty search results', async () => {
    vi.mocked(searchAndTransformDiscItAPI).mockResolvedValue({
      success: true,
      data: [],
      total: 0,
    });

    render(<DatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

    // Perform search
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /search discs/i })).toBeInTheDocument();
    });

    fireEvent.click(screen.getByRole('button', { name: /search discs/i }));

    // Wait for results section
    await waitFor(() => {
      expect(screen.getByText('Search Results')).toBeInTheDocument();
    });

    // Check empty state message
    expect(screen.getByText('No discs found matching your search criteria.')).toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /import selected/i })).not.toBeInTheDocument();
  });

  it('should apply filters correctly', async () => {
    render(<DatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByLabelText('Brand')).toBeInTheDocument();
    });

    // Set filters
    const brandSelect = screen.getByLabelText('Brand');
    const categorySelect = screen.getByLabelText('Category');
    const speedSelect = screen.getByLabelText('Speed');

    fireEvent.change(brandSelect, { target: { value: 'Innova' } });
    fireEvent.change(categorySelect, { target: { value: 'Distance Driver' } });
    fireEvent.change(speedSelect, { target: { value: '12' } });

    // Mock search with filters
    vi.mocked(searchAndTransformDiscItAPI).mockResolvedValue({
      success: true,
      data: [mockDiscs[0]], // Only Destroyer matches
      total: 1,
    });

    // Perform search
    fireEvent.click(screen.getByRole('button', { name: /search discs/i }));

    // Verify API was called with correct filters
    await waitFor(() => {
      expect(searchAndTransformDiscItAPI).toHaveBeenCalledWith({
        brand: 'Innova',
        category: 'Distance Driver',
        speed: 12,
        limit: 50,
      });
    });
  });

  it('should show loading state during search', async () => {
    // Mock a delayed response
    vi.mocked(searchAndTransformDiscItAPI).mockImplementation(
      () => new Promise(resolve => setTimeout(() => resolve({
        success: true,
        data: mockDiscs,
        total: 2,
      }), 100))
    );

    render(<DatabaseSearchTab onDiscSelect={mockOnDiscSelect} isLoading={false} />);

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /search discs/i })).toBeInTheDocument();
    });

    // Start search
    fireEvent.click(screen.getByRole('button', { name: /search discs/i }));

    // Check loading state
    expect(screen.getByRole('button', { name: /searching.../i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /searching.../i })).toBeDisabled();

    // Wait for search to complete
    await waitFor(() => {
      expect(screen.getByText('Search Results')).toBeInTheDocument();
    });
  });
});
