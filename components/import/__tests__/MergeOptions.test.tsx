/**
 * MergeOptions Component Tests
 *
 * Unit tests for the MergeOptions component covering strategy selection,
 * impact preview, and import execution.
 */

import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { MergeOptions } from "../MergeOptions";
import type { Disc } from "@/lib/types";
import { DiscCondition, Location } from "@/lib/types";

// Test data
const mockDisc: Disc = {
  id: "test-disc-1",
  manufacturer: "Innova",
  mold: "Destroyer",
  plasticType: "Champion",
  weight: 175,
  condition: DiscCondition.NEW,
  flightNumbers: { speed: 12, glide: 5, turn: -1, fade: 3 },
  color: "Blue",
  currentLocation: Location.BAG,
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-01"),
};

const mockImportResult = {
  success: true,
  data: [mockDisc],
  summary: {
    totalImported: 1,
    totalSkipped: 0,
    totalErrors: 0,
    newDiscs: 1,
    duplicates: 0,
    replacements: 0,
  },
};

describe("MergeOptions", () => {
  const mockOnMergeStrategyChange = vi.fn();
  const mockOnNext = vi.fn();
  const mockOnPrevious = vi.fn();
  const mockOnCancel = vi.fn();

  const defaultProps = {
    importResult: mockImportResult,
    currentDiscs: [],
    mergeStrategy: "merge" as const,
    onMergeStrategyChange: mockOnMergeStrategyChange,
    onNext: mockOnNext,
    onPrevious: mockOnPrevious,
    onCancel: mockOnCancel,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Initial Render", () => {
    it("renders import options header", () => {
      render(<MergeOptions {...defaultProps} />);
      
      expect(screen.getByText("Import Options")).toBeInTheDocument();
      expect(screen.getByText(/choose how to handle the imported data/i)).toBeInTheDocument();
    });

    it("shows all merge strategy options", () => {
      render(<MergeOptions {...defaultProps} />);
      
      expect(screen.getByText("Merge with Existing")).toBeInTheDocument();
      expect(screen.getByText("Append Only")).toBeInTheDocument();
      expect(screen.getByText("Replace Collection")).toBeInTheDocument();
    });

    it("shows merge strategy as selected by default", () => {
      render(<MergeOptions {...defaultProps} />);
      
      const mergeOption = screen.getByLabelText("Merge with Existing");
      expect(mergeOption).toBeChecked();
    });

    it("shows recommended badge for merge strategy", () => {
      render(<MergeOptions {...defaultProps} />);
      
      expect(screen.getByText("Recommended")).toBeInTheDocument();
    });
  });

  describe("Strategy Selection", () => {
    it("calls onMergeStrategyChange when strategy is changed", async () => {
      render(<MergeOptions {...defaultProps} />);
      
      const appendOption = screen.getByLabelText("Append Only");
      await userEvent.click(appendOption);
      
      expect(mockOnMergeStrategyChange).toHaveBeenCalledWith("append");
    });

    it("shows warning for replace strategy", async () => {
      render(<MergeOptions {...defaultProps} mergeStrategy="replace" />);
      
      expect(screen.getByText(/this will permanently delete your current collection/i)).toBeInTheDocument();
    });

    it("shows additional warning for replace when current collection exists", () => {
      const currentDiscs = [mockDisc];
      
      render(<MergeOptions {...defaultProps} currentDiscs={currentDiscs} mergeStrategy="replace" />);
      
      expect(screen.getByText(/this action cannot be undone/i)).toBeInTheDocument();
      expect(screen.getByText(/consider exporting your current collection/i)).toBeInTheDocument();
    });
  });

  describe("Import Impact", () => {
    it("shows correct impact for merge strategy", () => {
      render(<MergeOptions {...defaultProps} />);
      
      expect(screen.getByText("Merge with existing collection")).toBeInTheDocument();
      expect(screen.getByText(/1 new discs added, 0 existing discs updated/i)).toBeInTheDocument();
      expect(screen.getByText("Final collection size: 1 discs")).toBeInTheDocument();
    });

    it("shows correct impact for replace strategy", () => {
      const currentDiscs = [mockDisc, { ...mockDisc, id: "disc-2" }];
      
      render(<MergeOptions {...defaultProps} currentDiscs={currentDiscs} mergeStrategy="replace" />);
      
      expect(screen.getByText("Replace entire collection")).toBeInTheDocument();
      expect(screen.getByText(/2 discs will be removed, 1 discs will be added/i)).toBeInTheDocument();
      expect(screen.getByText("Final collection size: 1 discs")).toBeInTheDocument();
    });

    it("shows correct impact for append strategy", () => {
      const currentDiscs = [mockDisc];
      
      render(<MergeOptions {...defaultProps} currentDiscs={currentDiscs} mergeStrategy="append" />);
      
      expect(screen.getByText("Append new discs only")).toBeInTheDocument();
      expect(screen.getByText(/1 new discs added, 0 duplicates skipped/i)).toBeInTheDocument();
      expect(screen.getByText("Final collection size: 2 discs")).toBeInTheDocument();
    });

    it("handles duplicates in impact calculation", () => {
      const importResultWithDuplicates = {
        ...mockImportResult,
        summary: {
          ...mockImportResult.summary,
          duplicates: 1,
          newDiscs: 0,
        },
      };
      
      render(<MergeOptions {...defaultProps} importResult={importResultWithDuplicates} mergeStrategy="merge" />);
      
      expect(screen.getByText(/0 new discs added, 1 existing discs updated/i)).toBeInTheDocument();
    });
  });

  describe("Import Execution", () => {
    it("calls onNext when import button is clicked", async () => {
      render(<MergeOptions {...defaultProps} />);
      
      const importButton = screen.getByRole("button", { name: /import collection/i });
      await userEvent.click(importButton);
      
      expect(mockOnNext).toHaveBeenCalled();
    });

    it("shows loading state during import", async () => {
      // Mock a delayed onNext to test loading state
      const delayedOnNext = vi.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
      
      render(<MergeOptions {...defaultProps} onNext={delayedOnNext} />);
      
      const importButton = screen.getByRole("button", { name: /import collection/i });
      await userEvent.click(importButton);
      
      expect(screen.getByText("Importing...")).toBeInTheDocument();
      expect(importButton).toBeDisabled();
      
      await waitFor(() => {
        expect(delayedOnNext).toHaveBeenCalled();
      });
    });

    it("disables buttons during import", async () => {
      const delayedOnNext = vi.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
      
      render(<MergeOptions {...defaultProps} onNext={delayedOnNext} />);
      
      const importButton = screen.getByRole("button", { name: /import collection/i });
      const backButton = screen.getByRole("button", { name: /back to preview/i });
      const cancelButton = screen.getByRole("button", { name: /cancel/i });
      
      await userEvent.click(importButton);
      
      expect(importButton).toBeDisabled();
      expect(backButton).toBeDisabled();
      expect(cancelButton).toBeDisabled();
    });
  });

  describe("Navigation", () => {
    it("calls onPrevious when back button is clicked", async () => {
      render(<MergeOptions {...defaultProps} />);
      
      const backButton = screen.getByRole("button", { name: /back to preview/i });
      await userEvent.click(backButton);
      
      expect(mockOnPrevious).toHaveBeenCalled();
    });

    it("calls onCancel when cancel button is clicked", async () => {
      render(<MergeOptions {...defaultProps} />);
      
      const cancelButton = screen.getByRole("button", { name: /cancel/i });
      await userEvent.click(cancelButton);
      
      expect(mockOnCancel).toHaveBeenCalled();
    });
  });

  describe("Strategy Descriptions", () => {
    it("shows correct descriptions for each strategy", () => {
      render(<MergeOptions {...defaultProps} />);
      
      expect(screen.getByText(/add new discs and update existing ones/i)).toBeInTheDocument();
      expect(screen.getByText(/add only new discs. skip any duplicates/i)).toBeInTheDocument();
      expect(screen.getByText(/replace your entire collection/i)).toBeInTheDocument();
    });
  });

  describe("Edge Cases", () => {
    it("handles missing summary data gracefully", () => {
      const importResultWithoutSummary = {
        ...mockImportResult,
        summary: undefined,
      };
      
      render(<MergeOptions {...defaultProps} importResult={importResultWithoutSummary} />);
      
      // Should not crash and show default values
      expect(screen.getByText("Final collection size:")).toBeInTheDocument();
    });

    it("handles empty import data", () => {
      const emptyImportResult = {
        success: true,
        data: [],
        summary: {
          totalImported: 0,
          totalSkipped: 0,
          totalErrors: 0,
          newDiscs: 0,
          duplicates: 0,
          replacements: 0,
        },
      };
      
      render(<MergeOptions {...defaultProps} importResult={emptyImportResult} />);
      
      expect(screen.getByText(/0 new discs added/i)).toBeInTheDocument();
    });
  });
});
