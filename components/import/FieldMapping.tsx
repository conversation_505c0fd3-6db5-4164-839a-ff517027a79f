/**
 * FieldMapping Component for CSV Import
 *
 * Allows users to map CSV columns to disc fields for flexible import.
 */

"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ArrowLeft, ArrowRight, Info, CheckCircle, AlertTriangle, Save, Bookmark } from "lucide-react";
import { cn } from "@/lib/utils";
import { getAllTemplates, createTemplate, type ImportTemplate } from "@/lib/importTemplates";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Field mapping configuration
 */
export interface FieldMapping {
  csvColumn: string;
  discField: string | null;
  isRequired: boolean;
  dataType: "string" | "number" | "date" | "enum";
  enumValues?: string[];
}

/**
 * CSV preview data
 */
export interface CSVPreviewData {
  headers: string[];
  rows: string[][];
  totalRows: number;
}

/**
 * Field mapping props
 */
export interface FieldMappingProps {
  csvData: CSVPreviewData;
  onMappingComplete: (mappings: Record<string, string>) => void;
  onPrevious: () => void;
  onCancel: () => void;
  className?: string;
}

// ============================================================================
// FIELD DEFINITIONS
// ============================================================================

/**
 * Available disc fields for mapping
 */
interface DiscFieldDefinition {
  key: string;
  label: string;
  required: boolean;
  dataType: "string" | "number" | "date" | "enum";
  enumValues?: string[];
  description: string;
}

const DISC_FIELDS: DiscFieldDefinition[] = [
  {
    key: "manufacturer",
    label: "Manufacturer",
    required: true,
    dataType: "string",
    description: "Disc manufacturer (e.g., Innova, Discraft)",
  },
  {
    key: "mold",
    label: "Mold",
    required: true,
    dataType: "string",
    description: "Disc mold name (e.g., Destroyer, Buzzz)",
  },
  {
    key: "plasticType",
    label: "Plastic Type",
    required: false,
    dataType: "string",
    description: "Plastic material (e.g., Champion, ESP)",
  },
  {
    key: "weight",
    label: "Weight (grams)",
    required: false,
    dataType: "number",
    description: "Disc weight in grams (150-180)",
  },
  {
    key: "color",
    label: "Color",
    required: false,
    dataType: "string",
    description: "Primary disc color",
  },
  {
    key: "condition",
    label: "Condition",
    required: false,
    dataType: "enum",
    enumValues: ["new", "good", "fair", "worn", "damaged"],
    description: "Current disc condition",
  },
  {
    key: "currentLocation",
    label: "Current Location",
    required: false,
    dataType: "enum",
    enumValues: ["bag", "car", "home", "loaned", "lost"],
    description: "Where the disc is currently located",
  },
  {
    key: "flightNumbers.speed",
    label: "Speed",
    required: false,
    dataType: "number",
    description: "Flight number: Speed (1-15)",
  },
  {
    key: "flightNumbers.glide",
    label: "Glide",
    required: false,
    dataType: "number",
    description: "Flight number: Glide (1-7)",
  },
  {
    key: "flightNumbers.turn",
    label: "Turn",
    required: false,
    dataType: "number",
    description: "Flight number: Turn (-5 to +1)",
  },
  {
    key: "flightNumbers.fade",
    label: "Fade",
    required: false,
    dataType: "number",
    description: "Flight number: Fade (0-5)",
  },
  {
    key: "purchasePrice",
    label: "Purchase Price",
    required: false,
    dataType: "number",
    description: "Price paid for the disc",
  },
  {
    key: "purchaseDate",
    label: "Purchase Date",
    required: false,
    dataType: "date",
    description: "Date the disc was purchased",
  },
  {
    key: "notes",
    label: "Notes",
    required: false,
    dataType: "string",
    description: "Additional notes about the disc",
  },
];

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Auto-detect field mappings based on column names
 */
function autoDetectMappings(headers: string[]): Record<string, string> {
  const mappings: Record<string, string> = {};

  headers.forEach((header) => {
    const normalizedHeader = header.toLowerCase().trim();

    // Direct matches
    const directMatch = DISC_FIELDS.find(
      (field) => field.key.toLowerCase() === normalizedHeader || field.label.toLowerCase() === normalizedHeader
    );

    if (directMatch) {
      mappings[header] = directMatch.key;
      return;
    }

    // Partial matches
    if (normalizedHeader.includes("manufacturer") || normalizedHeader.includes("brand")) {
      mappings[header] = "manufacturer";
    } else if (normalizedHeader.includes("mold") || normalizedHeader.includes("disc")) {
      mappings[header] = "mold";
    } else if (normalizedHeader.includes("plastic") || normalizedHeader.includes("material")) {
      mappings[header] = "plasticType";
    } else if (normalizedHeader.includes("weight")) {
      mappings[header] = "weight";
    } else if (normalizedHeader.includes("color")) {
      mappings[header] = "color";
    } else if (normalizedHeader.includes("condition")) {
      mappings[header] = "condition";
    } else if (normalizedHeader.includes("location")) {
      mappings[header] = "currentLocation";
    } else if (normalizedHeader.includes("speed")) {
      mappings[header] = "flightNumbers.speed";
    } else if (normalizedHeader.includes("glide")) {
      mappings[header] = "flightNumbers.glide";
    } else if (normalizedHeader.includes("turn")) {
      mappings[header] = "flightNumbers.turn";
    } else if (normalizedHeader.includes("fade")) {
      mappings[header] = "flightNumbers.fade";
    } else if (normalizedHeader.includes("price")) {
      mappings[header] = "purchasePrice";
    } else if (normalizedHeader.includes("date") && normalizedHeader.includes("purchase")) {
      mappings[header] = "purchaseDate";
    } else if (normalizedHeader.includes("notes") || normalizedHeader.includes("comment")) {
      mappings[header] = "notes";
    }
  });

  return mappings;
}

/**
 * Validate field mappings
 */
function validateMappings(mappings: Record<string, string>): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check required fields
  const requiredFields = DISC_FIELDS.filter((field) => field.required);
  const mappedFields = Object.values(mappings);

  requiredFields.forEach((field) => {
    if (!mappedFields.includes(field.key)) {
      errors.push(`Required field "${field.label}" is not mapped`);
    }
  });

  // Check for duplicate mappings
  const fieldCounts: Record<string, number> = {};
  Object.values(mappings).forEach((field) => {
    if (field) {
      fieldCounts[field] = (fieldCounts[field] || 0) + 1;
    }
  });

  Object.entries(fieldCounts).forEach(([field, count]) => {
    if (count > 1) {
      const fieldDef = DISC_FIELDS.find((f) => f.key === field);
      warnings.push(`Field "${fieldDef?.label || field}" is mapped multiple times`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * FieldMapping component for CSV import field mapping
 */
export function FieldMapping({ csvData, onMappingComplete, onPrevious, onCancel, className }: FieldMappingProps) {
  const [mappings, setMappings] = React.useState<Record<string, string>>(() => autoDetectMappings(csvData.headers));
  const [templates, setTemplates] = React.useState<ImportTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = React.useState<ImportTemplate | null>(null);
  const [showSaveTemplate, setShowSaveTemplate] = React.useState(false);
  const [templateName, setTemplateName] = React.useState("");

  // Load templates on mount
  React.useEffect(() => {
    setTemplates(getAllTemplates());
  }, []);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  /**
   * Update field mapping
   */
  const updateMapping = (csvColumn: string, discField: string | null) => {
    setMappings((prev) => ({
      ...prev,
      [csvColumn]: discField || "",
    }));
  };

  /**
   * Apply template to current mappings
   */
  const handleApplyTemplate = (template: ImportTemplate) => {
    setMappings(template.fieldMappings);
    setSelectedTemplate(template);
  };

  /**
   * Save current mappings as template
   */
  const handleSaveTemplate = () => {
    if (!templateName.trim()) return;

    try {
      const template = createTemplate({
        name: templateName,
        description: `Template created from ${csvData.headers.length} column CSV`,
        fieldMappings: mappings,
        fileFormat: "csv",
        tags: ["custom"],
      });

      setTemplates((prev) => [...prev, template]);
      setTemplateName("");
      setShowSaveTemplate(false);
    } catch (error) {
      console.error("Failed to save template:", error);
    }
  };

  /**
   * Handle mapping completion
   */
  const handleComplete = () => {
    const validation = validateMappings(mappings);
    if (validation.isValid) {
      // Filter out empty mappings
      const filteredMappings = Object.fromEntries(
        Object.entries(mappings).filter(([, value]) => value && value.trim() !== "")
      );
      onMappingComplete(filteredMappings);
    }
  };

  // ============================================================================
  // VALIDATION
  // ============================================================================

  const validation = validateMappings(mappings);

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle>Map CSV Fields</CardTitle>
          <CardDescription>
            Map your CSV columns to disc fields. Required fields must be mapped to proceed.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Template Selection */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Templates</h4>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={() => setShowSaveTemplate(true)}>
                  <Save className="h-4 w-4 mr-2" />
                  Save as Template
                </Button>
              </div>
            </div>

            {templates.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {templates.slice(0, 6).map((template) => (
                  <Button
                    key={template.id}
                    variant={selectedTemplate?.id === template.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleApplyTemplate(template)}
                    className="justify-start text-left h-auto p-3"
                  >
                    <div>
                      <div className="font-medium text-sm">{template.name}</div>
                      <div className="text-xs text-muted-foreground truncate">
                        {Object.keys(template.fieldMappings).length} mappings
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            )}
          </div>

          {/* Save Template Dialog */}
          {showSaveTemplate && (
            <Alert>
              <Bookmark className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-3">
                  <div>Save current field mappings as a reusable template:</div>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      placeholder="Template name"
                      value={templateName}
                      onChange={(e) => setTemplateName(e.target.value)}
                      className="flex-1 px-3 py-1 border rounded text-sm"
                    />
                    <Button size="sm" onClick={handleSaveTemplate} disabled={!templateName.trim()}>
                      Save
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => setShowSaveTemplate(false)}>
                      Cancel
                    </Button>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Auto-detection Info */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              We've automatically detected some field mappings based on your column names. Review and adjust as needed.
            </AlertDescription>
          </Alert>

          {/* Field Mapping Table */}
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>CSV Column</TableHead>
                  <TableHead>Sample Data</TableHead>
                  <TableHead>Maps To</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {csvData.headers.map((header, index) => {
                  const mappedField = mappings[header];
                  const fieldDef = DISC_FIELDS.find((f) => f.key === mappedField);
                  const sampleData = csvData.rows[0]?.[index] || "";

                  return (
                    <TableRow key={header}>
                      <TableCell className="font-medium">{header}</TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {sampleData.length > 20 ? `${sampleData.substring(0, 20)}...` : sampleData}
                      </TableCell>
                      <TableCell>
                        <Select
                          value={mappedField || ""}
                          onValueChange={(value) => updateMapping(header, value || null)}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select field..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">Don't import</SelectItem>
                            {DISC_FIELDS.map((field) => (
                              <SelectItem key={field.key} value={field.key}>
                                {field.label}
                                {field.required && <span className="text-red-500 ml-1">*</span>}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        {fieldDef ? (
                          <div className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            {fieldDef.required && <Badge variant="destructive">Required</Badge>}
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <AlertTriangle className="h-4 w-4 text-orange-600" />
                            <span className="text-sm text-muted-foreground">Not mapped</span>
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {/* Validation Results */}
          {validation.errors.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <div className="font-medium">Required fields missing:</div>
                  <ul className="list-disc list-inside text-sm">
                    {validation.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {validation.warnings.length > 0 && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <div className="font-medium">Warnings:</div>
                  <ul className="list-disc list-inside text-sm">
                    {validation.warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrevious}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Upload
        </Button>
        <div className="space-x-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={handleComplete} disabled={!validation.isValid}>
            <span>Continue to Preview</span>
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
}
