/**
 * BatchImportWizard Component for Disc Golf Inventory Management System
 *
 * A comprehensive wizard for importing multiple files simultaneously with
 * parallel processing, progress tracking, and consolidated results.
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Settings,
  Download,
  AlertTriangle,
  Info
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { Disc } from "@/lib/types";
import {
  createBatchImportFiles,
  processBatchImport,
  DEFAULT_BATCH_OPTIONS,
  type BatchImportFile,
  type BatchImportProgress,
  type BatchImportResult,
  type BatchImportOptions,
} from "@/lib/batchImport";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Batch import wizard props
 */
export interface BatchImportWizardProps {
  currentDiscs: Disc[];
  onImportComplete: (discs: Disc[], strategy: "replace" | "merge" | "append") => Promise<void>;
  onCancel: () => void;
  className?: string;
}

/**
 * Batch import wizard state
 */
interface BatchImportWizardState {
  step: "upload" | "configure" | "processing" | "results";
  files: BatchImportFile[];
  options: BatchImportOptions;
  progress: BatchImportProgress | null;
  result: BatchImportResult | null;
  isProcessing: boolean;
  error: string | null;
}

// ============================================================================
// HELPER COMPONENTS
// ============================================================================

/**
 * File list component
 */
interface FileListProps {
  files: BatchImportFile[];
  onRemoveFile: (fileId: string) => void;
  className?: string;
}

function FileList({ files, onRemoveFile, className }: FileListProps) {
  const getStatusIcon = (status: BatchImportFile["status"]) => {
    switch (status) {
      case "pending":
        return <FileText className="h-4 w-4 text-muted-foreground" />;
      case "processing":
        return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />;
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-600" />;
    }
  };

  const getStatusBadge = (status: BatchImportFile["status"]) => {
    const variants = {
      pending: "secondary",
      processing: "default",
      completed: "default",
      failed: "destructive",
    } as const;

    const colors = {
      pending: "text-muted-foreground",
      processing: "text-blue-600",
      completed: "text-green-600",
      failed: "text-red-600",
    };

    return (
      <Badge variant={variants[status]} className={colors[status]}>
        {status}
      </Badge>
    );
  };

  return (
    <div className={cn("space-y-3", className)}>
      {files.map((file) => (
        <div key={file.id} className="flex items-center space-x-3 p-3 border rounded-lg">
          {getStatusIcon(file.status)}
          <div className="flex-1 min-w-0">
            <div className="font-medium truncate">{file.file.name}</div>
            <div className="text-sm text-muted-foreground">
              {(file.file.size / 1024).toFixed(1)} KB
              {file.status === "processing" && ` • ${file.progress}%`}
              {file.error && ` • ${file.error}`}
            </div>
            {file.status === "processing" && (
              <Progress value={file.progress} className="h-1 mt-1" />
            )}
          </div>
          <div className="flex items-center space-x-2">
            {getStatusBadge(file.status)}
            {file.status === "pending" && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemoveFile(file.id)}
              >
                Remove
              </Button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

/**
 * Batch progress component
 */
interface BatchProgressProps {
  progress: BatchImportProgress;
  className?: string;
}

function BatchProgress({ progress, className }: BatchProgressProps) {
  const getStageMessage = () => {
    switch (progress.stage) {
      case "preparing":
        return "Preparing files for import...";
      case "processing":
        return `Processing files (${progress.completedFiles}/${progress.totalFiles})`;
      case "consolidating":
        return "Consolidating results...";
      case "completed":
        return "Import completed!";
    }
  };

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Loader2 className="h-5 w-5 animate-spin text-primary" />
          <span>Batch Import Progress</span>
        </CardTitle>
        <CardDescription>{getStageMessage()}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{progress.overallProgress}%</span>
          </div>
          <Progress value={progress.overallProgress} className="h-2" />
        </div>

        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="space-y-1">
            <div className="text-2xl font-bold text-green-600">{progress.completedFiles}</div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-blue-600">
              {progress.totalFiles - progress.completedFiles - progress.failedFiles}
            </div>
            <div className="text-sm text-muted-foreground">Processing</div>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-red-600">{progress.failedFiles}</div>
            <div className="text-sm text-muted-foreground">Failed</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Batch results component
 */
interface BatchResultsProps {
  result: BatchImportResult;
  onRetry: () => void;
  onComplete: () => void;
  className?: string;
}

function BatchResults({ result, onRetry, onComplete, className }: BatchResultsProps) {
  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            {result.success ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <AlertTriangle className="h-5 w-5 text-orange-600" />
            )}
            <span>Batch Import Results</span>
          </CardTitle>
          <CardDescription>
            {result.success
              ? "All files imported successfully!"
              : `${result.failedFiles} of ${result.totalFiles} files failed to import.`}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Summary Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center space-y-1">
              <div className="text-2xl font-bold text-blue-600">{result.totalFiles}</div>
              <div className="text-sm text-muted-foreground">Total Files</div>
            </div>
            <div className="text-center space-y-1">
              <div className="text-2xl font-bold text-green-600">{result.successfulFiles}</div>
              <div className="text-sm text-muted-foreground">Successful</div>
            </div>
            <div className="text-center space-y-1">
              <div className="text-2xl font-bold text-red-600">{result.failedFiles}</div>
              <div className="text-sm text-muted-foreground">Failed</div>
            </div>
            <div className="text-center space-y-1">
              <div className="text-2xl font-bold text-purple-600">{result.totalDiscsImported}</div>
              <div className="text-sm text-muted-foreground">Discs Imported</div>
            </div>
          </div>

          {/* Errors */}
          {result.errors.length > 0 && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <div className="font-medium">Import Errors:</div>
                  <ul className="list-disc list-inside text-sm space-y-1">
                    {result.errors.slice(0, 5).map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                    {result.errors.length > 5 && (
                      <li>... and {result.errors.length - 5} more errors</li>
                    )}
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Warnings */}
          {result.warnings.length > 0 && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <div className="font-medium">Warnings:</div>
                  <ul className="list-disc list-inside text-sm space-y-1">
                    {result.warnings.slice(0, 3).map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                    {result.warnings.length > 3 && (
                      <li>... and {result.warnings.length - 3} more warnings</li>
                    )}
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        {result.failedFiles > 0 ? (
          <Button variant="outline" onClick={onRetry}>
            Retry Failed Files
          </Button>
        ) : (
          <div />
        )}
        <Button onClick={onComplete}>
          <Download className="h-4 w-4 mr-2" />
          Complete Import
        </Button>
      </div>
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * BatchImportWizard component for importing multiple files
 */
export function BatchImportWizard({ currentDiscs, onImportComplete, onCancel, className }: BatchImportWizardProps) {
  const [state, setState] = React.useState<BatchImportWizardState>({
    step: "upload",
    files: [],
    options: DEFAULT_BATCH_OPTIONS,
    progress: null,
    result: null,
    isProcessing: false,
    error: null,
  });

  // ============================================================================
  // HANDLERS
  // ============================================================================

  /**
   * Handle file selection
   */
  const handleFileSelect = (selectedFiles: FileList | null) => {
    if (!selectedFiles) return;

    const newFiles = createBatchImportFiles(Array.from(selectedFiles));
    setState(prev => ({
      ...prev,
      files: [...prev.files, ...newFiles],
      error: null,
    }));
  };

  /**
   * Remove a file from the batch
   */
  const handleRemoveFile = (fileId: string) => {
    setState(prev => ({
      ...prev,
      files: prev.files.filter(f => f.id !== fileId),
    }));
  };

  /**
   * Start batch processing
   */
  const handleStartProcessing = async () => {
    if (state.files.length === 0) return;

    setState(prev => ({ ...prev, step: "processing", isProcessing: true, error: null }));

    try {
      const options: BatchImportOptions = {
        ...state.options,
        onProgress: (progress) => {
          setState(prev => ({ ...prev, progress }));
        },
        onFileComplete: (file) => {
          setState(prev => ({
            ...prev,
            files: prev.files.map(f => f.id === file.id ? file : f),
          }));
        },
      };

      const result = await processBatchImport(state.files, currentDiscs, options);

      setState(prev => ({
        ...prev,
        step: "results",
        result,
        isProcessing: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Batch import failed",
        isProcessing: false,
      }));
    }
  };

  /**
   * Complete the import
   */
  const handleCompleteImport = async () => {
    if (!state.result?.consolidatedData) return;

    try {
      await onImportComplete(state.result.consolidatedData, state.options.mergeStrategy);
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to complete import",
      }));
    }
  };

  // ============================================================================
  // RENDER STEP CONTENT
  // ============================================================================

  const renderStepContent = () => {
    switch (state.step) {
      case "upload":
        return (
          <Card>
            <CardHeader>
              <CardTitle>Select Multiple Files</CardTitle>
              <CardDescription>
                Choose multiple JSON or CSV files to import simultaneously.
                Files will be processed in parallel for faster imports.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* File Upload Area */}
              <div className="border-2 border-dashed rounded-lg p-8 text-center">
                <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">Drop multiple files here or click to browse</p>
                  <p className="text-sm text-muted-foreground">
                    Supports JSON and CSV files. You can select multiple files at once.
                  </p>
                </div>
                <input
                  type="file"
                  multiple
                  accept=".json,.csv"
                  onChange={(e) => handleFileSelect(e.target.files)}
                  className="hidden"
                  id="batch-file-input"
                />
                <label
                  htmlFor="batch-file-input"
                  className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 mt-4 cursor-pointer"
                >
                  Select Files
                </label>
              </div>

              {/* Selected Files */}
              {state.files.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium">Selected Files ({state.files.length})</h4>
                  <FileList files={state.files} onRemoveFile={handleRemoveFile} />
                </div>
              )}

              {/* Error Display */}
              {state.error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{state.error}</AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        );

      case "processing":
        return state.progress ? (
          <BatchProgress progress={state.progress} />
        ) : (
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Initializing batch import...</p>
          </div>
        );

      case "results":
        return state.result ? (
          <BatchResults
            result={state.result}
            onRetry={() => setState(prev => ({ ...prev, step: "upload" }))}
            onComplete={handleCompleteImport}
          />
        ) : null;

      default:
        return null;
    }
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className={cn("space-y-6", className)}>
      {/* Step Content */}
      {renderStepContent()}

      {/* Action Buttons */}
      {state.step === "upload" && (
        <div className="flex justify-between">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            onClick={handleStartProcessing}
            disabled={state.files.length === 0 || state.isProcessing}
          >
            <Settings className="h-4 w-4 mr-2" />
            Start Batch Import ({state.files.length} files)
          </Button>
        </div>
      )}
    </div>
  );
}
