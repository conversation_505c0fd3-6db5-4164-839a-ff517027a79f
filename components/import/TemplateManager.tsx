/**
 * TemplateManager Component for Import Templates
 *
 * Provides a comprehensive interface for managing field mapping templates
 * and presets with create, edit, delete, and import/export functionality.
 */

"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Download, 
  Upload, 
  Search,
  Star,
  Copy,
  CheckCircle,
  AlertTriangle
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  getAllTemplates,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  incrementTemplateUsage,
  validateTemplate,
  exportTemplates,
  importTemplates,
  type ImportTemplate,
} from "@/lib/importTemplates";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Template manager props
 */
export interface TemplateManagerProps {
  onTemplateSelect?: (template: ImportTemplate) => void;
  selectedTemplateId?: string;
  showSelection?: boolean;
  className?: string;
}

/**
 * Template form data
 */
interface TemplateFormData {
  name: string;
  description: string;
  fieldMappings: Record<string, string>;
  tags: string[];
}

// ============================================================================
// HELPER COMPONENTS
// ============================================================================

/**
 * Template card component
 */
interface TemplateCardProps {
  template: ImportTemplate;
  isSelected?: boolean;
  onSelect?: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onUse: () => void;
  className?: string;
}

function TemplateCard({ template, isSelected, onSelect, onEdit, onDelete, onUse, className }: TemplateCardProps) {
  return (
    <Card className={cn("cursor-pointer transition-colors", isSelected && "ring-2 ring-primary", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1" onClick={onSelect}>
            <CardTitle className="text-base flex items-center space-x-2">
              <span>{template.name}</span>
              {template.isBuiltIn && <Badge variant="secondary">Built-in</Badge>}
              {template.usageCount > 0 && (
                <Badge variant="outline" className="text-xs">
                  <Star className="h-3 w-3 mr-1" />
                  {template.usageCount}
                </Badge>
              )}
            </CardTitle>
            <CardDescription className="mt-1">{template.description}</CardDescription>
          </div>
          <div className="flex space-x-1">
            <Button variant="ghost" size="sm" onClick={onUse}>
              <Copy className="h-4 w-4" />
            </Button>
            {!template.isBuiltIn && (
              <>
                <Button variant="ghost" size="sm" onClick={onEdit}>
                  <Edit className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={onDelete}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="text-sm text-muted-foreground">
            {Object.keys(template.fieldMappings).length} field mappings
          </div>
          {template.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {template.tags.map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Template form component
 */
interface TemplateFormProps {
  template?: ImportTemplate;
  onSave: (data: TemplateFormData) => void;
  onCancel: () => void;
}

function TemplateForm({ template, onSave, onCancel }: TemplateFormProps) {
  const [formData, setFormData] = React.useState<TemplateFormData>({
    name: template?.name || "",
    description: template?.description || "",
    fieldMappings: template?.fieldMappings || {},
    tags: template?.tags || [],
  });

  const [errors, setErrors] = React.useState<string[]>([]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validateTemplate(formData);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    onSave(formData);
  };

  const handleTagsChange = (value: string) => {
    const tags = value.split(",").map(tag => tag.trim()).filter(Boolean);
    setFormData(prev => ({ ...prev, tags }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {errors.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <ul className="list-disc list-inside">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-2">
        <Label htmlFor="name">Template Name</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          placeholder="Enter template name"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Describe what this template is for"
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="tags">Tags (comma-separated)</Label>
        <Input
          id="tags"
          value={formData.tags.join(", ")}
          onChange={(e) => handleTagsChange(e.target.value)}
          placeholder="e.g., standard, flight-numbers, inventory"
        />
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {template ? "Update Template" : "Create Template"}
        </Button>
      </div>
    </form>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * TemplateManager component for managing import templates
 */
export function TemplateManager({ onTemplateSelect, selectedTemplateId, showSelection, className }: TemplateManagerProps) {
  const [templates, setTemplates] = React.useState<ImportTemplate[]>([]);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [showCreateDialog, setShowCreateDialog] = React.useState(false);
  const [editingTemplate, setEditingTemplate] = React.useState<ImportTemplate | null>(null);
  const [loading, setLoading] = React.useState(false);
  const [message, setMessage] = React.useState<{ type: "success" | "error"; text: string } | null>(null);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  React.useEffect(() => {
    loadTemplates();
  }, []);

  React.useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const loadTemplates = () => {
    try {
      const allTemplates = getAllTemplates();
      setTemplates(allTemplates);
    } catch (error) {
      setMessage({ type: "error", text: "Failed to load templates" });
    }
  };

  const handleCreateTemplate = async (data: TemplateFormData) => {
    try {
      setLoading(true);
      const newTemplate = createTemplate(data);
      setTemplates(prev => [...prev, newTemplate]);
      setShowCreateDialog(false);
      setMessage({ type: "success", text: "Template created successfully" });
    } catch (error) {
      setMessage({ type: "error", text: error instanceof Error ? error.message : "Failed to create template" });
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateTemplate = async (data: TemplateFormData) => {
    if (!editingTemplate) return;

    try {
      setLoading(true);
      const updatedTemplate = updateTemplate(editingTemplate.id, data);
      if (updatedTemplate) {
        setTemplates(prev => prev.map(t => t.id === updatedTemplate.id ? updatedTemplate : t));
        setEditingTemplate(null);
        setMessage({ type: "success", text: "Template updated successfully" });
      }
    } catch (error) {
      setMessage({ type: "error", text: error instanceof Error ? error.message : "Failed to update template" });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTemplate = async (template: ImportTemplate) => {
    if (!confirm(`Are you sure you want to delete "${template.name}"?`)) return;

    try {
      const success = deleteTemplate(template.id);
      if (success) {
        setTemplates(prev => prev.filter(t => t.id !== template.id));
        setMessage({ type: "success", text: "Template deleted successfully" });
      }
    } catch (error) {
      setMessage({ type: "error", text: error instanceof Error ? error.message : "Failed to delete template" });
    }
  };

  const handleUseTemplate = (template: ImportTemplate) => {
    incrementTemplateUsage(template.id);
    setTemplates(prev => prev.map(t => 
      t.id === template.id ? { ...t, usageCount: t.usageCount + 1 } : t
    ));
    
    if (onTemplateSelect) {
      onTemplateSelect(template);
    }
  };

  const handleExportTemplates = () => {
    try {
      const exportData = exportTemplates();
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `import-templates-${new Date().toISOString().split("T")[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);
      setMessage({ type: "success", text: "Templates exported successfully" });
    } catch (error) {
      setMessage({ type: "error", text: "Failed to export templates" });
    }
  };

  const handleImportTemplates = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const exportData = JSON.parse(e.target?.result as string);
        const result = importTemplates(exportData);
        
        if (result.imported > 0) {
          loadTemplates();
          setMessage({ 
            type: "success", 
            text: `Imported ${result.imported} templates${result.skipped > 0 ? `, skipped ${result.skipped}` : ""}` 
          });
        } else {
          setMessage({ type: "error", text: "No templates were imported" });
        }
      } catch (error) {
        setMessage({ type: "error", text: "Failed to import templates" });
      }
    };
    reader.readAsText(file);
    
    // Reset input
    event.target.value = "";
  };

  // ============================================================================
  // FILTERING
  // ============================================================================

  const filteredTemplates = templates.filter(template =>
    template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Import Templates</h2>
          <p className="text-muted-foreground">Manage field mapping presets for CSV imports</p>
        </div>
        <div className="flex space-x-2">
          <input
            type="file"
            accept=".json"
            onChange={handleImportTemplates}
            className="hidden"
            id="import-templates"
          />
          <label htmlFor="import-templates">
            <Button variant="outline" asChild>
              <span>
                <Upload className="h-4 w-4 mr-2" />
                Import
              </span>
            </Button>
          </label>
          <Button variant="outline" onClick={handleExportTemplates}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Template
          </Button>
        </div>
      </div>

      {/* Message */}
      {message && (
        <Alert variant={message.type === "error" ? "destructive" : "default"}>
          {message.type === "success" ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <AlertTriangle className="h-4 w-4" />
          )}
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search templates..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredTemplates.map((template) => (
          <TemplateCard
            key={template.id}
            template={template}
            isSelected={showSelection && selectedTemplateId === template.id}
            onSelect={showSelection ? () => onTemplateSelect?.(template) : undefined}
            onEdit={() => setEditingTemplate(template)}
            onDelete={() => handleDeleteTemplate(template)}
            onUse={() => handleUseTemplate(template)}
          />
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            {searchQuery ? "No templates match your search." : "No templates found."}
          </p>
        </div>
      )}

      {/* Create Template Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Template</DialogTitle>
            <DialogDescription>
              Create a new field mapping template for CSV imports.
            </DialogDescription>
          </DialogHeader>
          <TemplateForm
            onSave={handleCreateTemplate}
            onCancel={() => setShowCreateDialog(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Template Dialog */}
      <Dialog open={!!editingTemplate} onOpenChange={() => setEditingTemplate(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Template</DialogTitle>
            <DialogDescription>
              Update the field mapping template.
            </DialogDescription>
          </DialogHeader>
          {editingTemplate && (
            <TemplateForm
              template={editingTemplate}
              onSave={handleUpdateTemplate}
              onCancel={() => setEditingTemplate(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
