/**
 * FileUploader Component for Import Wizard
 *
 * Handles file selection with drag-and-drop support for JSON and CSV files.
 */

"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Upload, FileText, AlertCircle, Loader2, Globe, Clipboard, Type, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import type { FileUploaderProps } from "./types";

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * FileUploader component for selecting import files
 */
export function FileUploader({
  onFileSelect,
  selectedFile,
  isProcessing,
  error,
  onNext,
  onCancel,
  className,
}: FileUploaderProps) {
  const [dragActive, setDragActive] = React.useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  /**
   * Handle file input change
   */
  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onFileSelect(file);
    }
  };

  /**
   * Handle drag events
   */
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setDragActive(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragActive(false);

    const file = event.dataTransfer.files[0];
    if (file) {
      onFileSelect(file);
    }
  };

  /**
   * Open file browser
   */
  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  /**
   * Validate file and proceed
   */
  const handleNext = () => {
    if (selectedFile && !isProcessing) {
      onNext();
    }
  };

  // ============================================================================
  // VALIDATION
  // ============================================================================

  const isValidFile =
    selectedFile &&
    (selectedFile.name.toLowerCase().endsWith(".json") || selectedFile.name.toLowerCase().endsWith(".csv"));

  const canProceed = isValidFile && !isProcessing;

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle>Upload Collection Data</CardTitle>
          <CardDescription>
            Select a JSON or CSV file containing your disc golf collection data. JSON files provide full data support,
            while CSV files are great for spreadsheet imports.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* File Drop Zone */}
          <div
            className={cn(
              "relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer",
              dragActive && "border-primary bg-primary/5",
              selectedFile && "border-green-500 bg-green-50",
              !selectedFile && !dragActive && "border-muted-foreground hover:border-primary hover:bg-muted/50"
            )}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleBrowseClick}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept=".json,.csv"
              onChange={handleFileInputChange}
              className="hidden"
            />

            <div className="space-y-4">
              {selectedFile ? (
                <>
                  <FileText className="h-12 w-12 mx-auto text-green-600" />
                  <div className="space-y-2">
                    <p className="text-lg font-medium text-green-700">{selectedFile.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {(selectedFile.size / 1024).toFixed(1)} KB • {selectedFile.type || "Unknown type"}
                    </p>
                  </div>
                </>
              ) : (
                <>
                  <Upload className="h-12 w-12 mx-auto text-muted-foreground" />
                  <div className="space-y-2">
                    <p className="text-lg font-medium">Drop your file here or click to browse</p>
                    <p className="text-sm text-muted-foreground">Supports JSON and CSV files up to 10MB</p>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* File Format Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-sm">JSON Format</h4>
              <p className="text-xs text-muted-foreground">
                Full data support including metadata, timestamps, and nested fields. Exported from this app or
                compatible systems.
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-sm">CSV Format</h4>
              <p className="text-xs text-muted-foreground">
                Spreadsheet-compatible format. Flight numbers should use columns like "flightNumbers.speed",
                "flightNumbers.glide", etc.
              </p>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* File Validation Warning */}
          {selectedFile && !isValidFile && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please select a valid JSON or CSV file. Other file types are not supported.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleNext} disabled={!canProceed} className="min-w-[120px]">
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            "Next: Preview Data"
          )}
        </Button>
      </div>
    </div>
  );
}
