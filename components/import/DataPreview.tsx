/**
 * DataPreview Component for Import Wizard
 *
 * Displays a preview of imported data with validation results and statistics.
 */

"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CheckCircle, AlertTriangle, Info, ArrowLeft, ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Disc } from "@/lib/types";
import type { DataPreviewProps } from "./types";

// ============================================================================
// HELPER COMPONENTS
// ============================================================================

/**
 * Import statistics summary
 */
interface ImportStatsProps {
  importResult: any;
  fileName: string;
}

function ImportStats({ importResult, fileName }: ImportStatsProps) {
  const { summary, data, validationErrors } = importResult;

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div className="text-center space-y-1">
        <div className="text-2xl font-bold text-green-600">{data?.length || 0}</div>
        <div className="text-sm text-muted-foreground">Valid Discs</div>
      </div>
      <div className="text-center space-y-1">
        <div className="text-2xl font-bold text-blue-600">{summary?.newDiscs || 0}</div>
        <div className="text-sm text-muted-foreground">New Discs</div>
      </div>
      <div className="text-center space-y-1">
        <div className="text-2xl font-bold text-orange-600">{summary?.duplicates || 0}</div>
        <div className="text-sm text-muted-foreground">Duplicates</div>
      </div>
      <div className="text-center space-y-1">
        <div className="text-2xl font-bold text-red-600">{validationErrors?.length || 0}</div>
        <div className="text-sm text-muted-foreground">Errors</div>
      </div>
    </div>
  );
}

/**
 * Data preview table
 */
interface DataTableProps {
  discs: Disc[];
  maxRows?: number;
}

function DataTable({ discs, maxRows = 10 }: DataTableProps) {
  const displayDiscs = discs.slice(0, maxRows);

  return (
    <div className="border rounded-lg overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Manufacturer</TableHead>
            <TableHead>Mold</TableHead>
            <TableHead>Plastic</TableHead>
            <TableHead>Weight</TableHead>
            <TableHead>Flight Numbers</TableHead>
            <TableHead>Condition</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {displayDiscs.map((disc, index) => (
            <TableRow key={disc.id || index}>
              <TableCell className="font-medium">{disc.manufacturer}</TableCell>
              <TableCell>{disc.mold}</TableCell>
              <TableCell>{disc.plasticType}</TableCell>
              <TableCell>{disc.weight}g</TableCell>
              <TableCell>
                {disc.flightNumbers ? (
                  <span className="text-sm">
                    {disc.flightNumbers.speed}|{disc.flightNumbers.glide}|
                    {disc.flightNumbers.turn}|{disc.flightNumbers.fade}
                  </span>
                ) : (
                  <span className="text-muted-foreground">-</span>
                )}
              </TableCell>
              <TableCell>
                <Badge variant="outline" className="text-xs">
                  {disc.condition}
                </Badge>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      {discs.length > maxRows && (
        <div className="p-3 text-center text-sm text-muted-foreground bg-muted/50">
          Showing {maxRows} of {discs.length} discs
        </div>
      )}
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * DataPreview component for reviewing imported data
 */
export function DataPreview({
  importResult,
  selectedFile,
  onNext,
  onPrevious,
  onCancel,
  className,
}: DataPreviewProps) {
  const { data, summary, validationErrors } = importResult;

  // ============================================================================
  // VALIDATION STATUS
  // ============================================================================

  const hasErrors = validationErrors && validationErrors.length > 0;
  const hasWarnings = summary && summary.duplicates > 0;
  const isValid = data && data.length > 0;

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span>Data Preview</span>
          </CardTitle>
          <CardDescription>
            Review your imported data before proceeding. File: {selectedFile.name}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Import Statistics */}
          <ImportStats importResult={importResult} fileName={selectedFile.name} />

          {/* Status Alerts */}
          {hasErrors && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {validationErrors!.length} validation error(s) found. Some discs may not be imported.
              </AlertDescription>
            </Alert>
          )}

          {hasWarnings && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                {summary!.duplicates} duplicate disc(s) detected. You can choose how to handle them in the next step.
              </AlertDescription>
            </Alert>
          )}

          {isValid && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                Data validation successful! {data!.length} disc(s) ready to import.
              </AlertDescription>
            </Alert>
          )}

          {/* Data Preview Table */}
          {data && data.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium">Data Preview</h4>
              <DataTable discs={data} maxRows={10} />
            </div>
          )}

          {/* Validation Errors */}
          {hasErrors && (
            <div className="space-y-3">
              <h4 className="font-medium text-red-600">Validation Errors</h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {validationErrors!.slice(0, 10).map((error, index) => (
                  <div key={index} className="text-sm p-2 bg-red-50 border border-red-200 rounded">
                    <span className="font-medium">Row {error.index + 1}:</span> {error.message}
                    {error.field && <span className="text-muted-foreground"> (Field: {error.field})</span>}
                  </div>
                ))}
                {validationErrors!.length > 10 && (
                  <div className="text-sm text-muted-foreground text-center">
                    ... and {validationErrors!.length - 10} more errors
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrevious}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Upload
        </Button>
        <div className="space-x-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={onNext} disabled={!isValid}>
            <span>Next: Import Options</span>
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
}
