# Export Components

This directory contains all components related to the export functionality of the Disc Golf Inventory Management System.

## Components

### ExportWizard
Main orchestrator component that manages the multi-step export process.

**Usage:**
```tsx
import { ExportWizard } from '@/components/export';

<ExportWizard
  discs={discs}
  onExportComplete={(data, filename) => console.log('Export completed')}
  onCancel={() => router.back()}
/>
```

### FormatSelector
Handles format selection (JSON/CSV) and export options.

**Features:**
- Format comparison with descriptions
- Format-specific options
- Automatic filename generation

### FieldSelector
Provides granular field selection with grouping.

**Features:**
- Grouped field organization
- Select all/none functionality
- Required field protection

### ExportPreview
Final step showing export summary and initiating download.

**Features:**
- Export summary
- Data preview
- File size estimation
- Progress indication

## Usage Example

```tsx
import { ExportWizard } from '@/components/export';

function ExportPage() {
  const { discs } = useInventory();
  
  return (
    <ExportWizard
      discs={discs}
      onExportComplete={(data, filename) => {
        console.log(`Exported ${filename}`);
      }}
      onCancel={() => window.history.back()}
    />
  );
}
```

## Types

All component types are exported from the index file:
- `ExportWizardProps`
- `ExportWizardState`
- `ExportStep`
- `FormatSelectorProps`
- `FieldSelectorProps`
- `ExportPreviewProps`

## Integration

These components integrate with:
- `lib/exportImport.ts` for export functionality
- `hooks/useInventory.ts` for data access
- Existing UI component library
- Next.js App Router for navigation
