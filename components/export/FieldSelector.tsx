/**
 * FieldSelector Component for Disc Golf Inventory Management System
 *
 * A component for selecting which fields to include in the export with checkboxes,
 * select all/none functionality, and field grouping.
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { ArrowRight, ArrowLeft, CheckSquare, Square, Minus } from "lucide-react";
import { cn } from "@/lib/utils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Field definition for export
 */
interface FieldDefinition {
  key: string;
  label: string;
  description: string;
  required?: boolean;
}

/**
 * Field group definition
 */
interface FieldGroup {
  key: string;
  label: string;
  description: string;
  fields: FieldDefinition[];
}

/**
 * FieldSelector component props
 */
export interface FieldSelectorProps {
  /** Currently selected fields */
  selectedFields: string[];
  /** Callback when field selection changes */
  onFieldSelectionChange: (fields: string[]) => void;
  /** Navigation callbacks */
  onNext: () => void;
  onPrevious: () => void;
  onCancel: () => void;
  /** Additional CSS classes */
  className?: string;
}

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * Available field groups for export
 */
const FIELD_GROUPS: FieldGroup[] = [
  {
    key: "basic",
    label: "Basic Information",
    description: "Essential disc identification and details",
    fields: [
      { key: "id", label: "ID", description: "Unique disc identifier", required: true },
      { key: "manufacturer", label: "Manufacturer", description: "Disc manufacturer (e.g., Innova, Discraft)" },
      { key: "mold", label: "Mold", description: "Disc mold/model name" },
      { key: "plasticType", label: "Plastic Type", description: "Type of plastic used" },
      { key: "weight", label: "Weight", description: "Disc weight in grams" },
      { key: "color", label: "Color", description: "Primary disc color" },
    ]
  },
  {
    key: "flight",
    label: "Flight Numbers",
    description: "Disc flight characteristics and performance",
    fields: [
      { key: "flightNumbers.speed", label: "Speed", description: "How fast the disc needs to be thrown (1-14)" },
      { key: "flightNumbers.glide", label: "Glide", description: "How long the disc stays in the air (1-7)" },
      { key: "flightNumbers.turn", label: "Turn", description: "High speed stability (-5 to +1)" },
      { key: "flightNumbers.fade", label: "Fade", description: "Low speed stability (0-5)" },
    ]
  },
  {
    key: "condition",
    label: "Condition & Location",
    description: "Current state and storage information",
    fields: [
      { key: "condition", label: "Condition", description: "Current wear state of the disc" },
      { key: "currentLocation", label: "Location", description: "Where the disc is currently stored" },
    ]
  },
  {
    key: "purchase",
    label: "Purchase Information",
    description: "Acquisition details and pricing",
    fields: [
      { key: "purchaseDate", label: "Purchase Date", description: "When the disc was acquired" },
      { key: "purchasePrice", label: "Purchase Price", description: "Price paid in USD" },
    ]
  },
  {
    key: "metadata",
    label: "Metadata",
    description: "Additional information and timestamps",
    fields: [
      { key: "notes", label: "Notes", description: "User notes and comments" },
      { key: "imageUrl", label: "Image URL", description: "Link to disc image" },
      { key: "createdAt", label: "Created At", description: "When the record was created" },
      { key: "updatedAt", label: "Updated At", description: "When the record was last modified" },
    ]
  }
];

/**
 * Get all available fields
 */
const ALL_FIELDS = FIELD_GROUPS.flatMap(group => group.fields.map(field => field.key));

/**
 * Get required fields
 */
const REQUIRED_FIELDS = FIELD_GROUPS.flatMap(group => 
  group.fields.filter(field => field.required).map(field => field.key)
);

// ============================================================================
// COMPONENTS
// ============================================================================

/**
 * Field group component
 */
function FieldGroupCard({
  group,
  selectedFields,
  onFieldToggle,
  onGroupToggle,
  className
}: {
  group: FieldGroup;
  selectedFields: string[];
  onFieldToggle: (fieldKey: string) => void;
  onGroupToggle: (groupKey: string, selected: boolean) => void;
  className?: string;
}) {
  const groupFields = group.fields.map(field => field.key);
  const selectedGroupFields = groupFields.filter(field => selectedFields.includes(field));
  const isAllSelected = selectedGroupFields.length === groupFields.length;
  const isPartiallySelected = selectedGroupFields.length > 0 && selectedGroupFields.length < groupFields.length;

  const handleGroupToggle = () => {
    onGroupToggle(group.key, !isAllSelected);
  };

  const getGroupIcon = () => {
    if (isAllSelected) return CheckSquare;
    if (isPartiallySelected) return Minus;
    return Square;
  };

  const GroupIcon = getGroupIcon();

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center space-x-3">
          <button
            onClick={handleGroupToggle}
            className="flex items-center space-x-2 hover:bg-muted/50 rounded p-1 transition-colors"
          >
            <GroupIcon className={cn(
              "h-5 w-5",
              isAllSelected && "text-primary",
              isPartiallySelected && "text-primary"
            )} />
            <div className="text-left">
              <CardTitle className="text-base">{group.label}</CardTitle>
              <CardDescription className="text-sm">
                {group.description}
              </CardDescription>
            </div>
          </button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {group.fields.map((field) => {
            const isSelected = selectedFields.includes(field.key);
            const isRequired = field.required;

            return (
              <div key={field.key} className="flex items-start space-x-3">
                <button
                  onClick={() => !isRequired && onFieldToggle(field.key)}
                  disabled={isRequired}
                  className={cn(
                    "flex items-center space-x-2 hover:bg-muted/50 rounded p-1 transition-colors flex-1 text-left",
                    isRequired && "opacity-75 cursor-not-allowed"
                  )}
                >
                  {isSelected ? (
                    <CheckSquare className="h-4 w-4 text-primary flex-shrink-0" />
                  ) : (
                    <Square className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  )}
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center space-x-2">
                      <Label className={cn(
                        "text-sm font-medium cursor-pointer",
                        isRequired && "cursor-not-allowed"
                      )}>
                        {field.label}
                      </Label>
                      {isRequired && (
                        <span className="text-xs text-muted-foreground bg-muted px-1 rounded">
                          Required
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {field.description}
                    </p>
                  </div>
                </button>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Selection summary component
 */
function SelectionSummary({
  selectedFields,
  onSelectAll,
  onSelectNone
}: {
  selectedFields: string[];
  onSelectAll: () => void;
  onSelectNone: () => void;
}) {
  const totalFields = ALL_FIELDS.length;
  const selectedCount = selectedFields.length;

  return (
    <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
      <div className="text-sm">
        <span className="font-medium">{selectedCount}</span> of{" "}
        <span className="font-medium">{totalFields}</span> fields selected
      </div>
      <div className="flex space-x-2">
        <Button variant="outline" size="sm" onClick={onSelectAll}>
          Select All
        </Button>
        <Button variant="outline" size="sm" onClick={onSelectNone}>
          Select None
        </Button>
      </div>
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * FieldSelector component for choosing which fields to export
 */
export function FieldSelector({
  selectedFields,
  onFieldSelectionChange,
  onNext,
  onPrevious,
  onCancel,
  className
}: FieldSelectorProps) {
  // Initialize with all fields if none selected
  React.useEffect(() => {
    if (selectedFields.length === 0) {
      onFieldSelectionChange(ALL_FIELDS);
    }
  }, [selectedFields.length, onFieldSelectionChange]);

  const handleFieldToggle = (fieldKey: string) => {
    if (selectedFields.includes(fieldKey)) {
      onFieldSelectionChange(selectedFields.filter(field => field !== fieldKey));
    } else {
      onFieldSelectionChange([...selectedFields, fieldKey]);
    }
  };

  const handleGroupToggle = (groupKey: string, selected: boolean) => {
    const group = FIELD_GROUPS.find(g => g.key === groupKey);
    if (!group) return;

    const groupFieldKeys = group.fields.map(field => field.key);
    
    if (selected) {
      // Add all group fields
      const newFields = [...new Set([...selectedFields, ...groupFieldKeys])];
      onFieldSelectionChange(newFields);
    } else {
      // Remove all group fields (except required ones)
      const fieldsToRemove = groupFieldKeys.filter(field => !REQUIRED_FIELDS.includes(field));
      const newFields = selectedFields.filter(field => !fieldsToRemove.includes(field));
      onFieldSelectionChange(newFields);
    }
  };

  const handleSelectAll = () => {
    onFieldSelectionChange(ALL_FIELDS);
  };

  const handleSelectNone = () => {
    onFieldSelectionChange(REQUIRED_FIELDS);
  };

  return (
    <Card className={cn("max-w-4xl mx-auto", className)}>
      <CardHeader className="text-center">
        <CardTitle className="text-2xl">Select Fields to Export</CardTitle>
        <CardDescription>
          Choose which disc information to include in your export
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Selection Summary */}
        <SelectionSummary
          selectedFields={selectedFields}
          onSelectAll={handleSelectAll}
          onSelectNone={handleSelectNone}
        />

        {/* Field Groups */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {FIELD_GROUPS.map((group) => (
            <FieldGroupCard
              key={group.key}
              group={group}
              selectedFields={selectedFields}
              onFieldToggle={handleFieldToggle}
              onGroupToggle={handleGroupToggle}
            />
          ))}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6 border-t">
          <Button variant="outline" onClick={onPrevious}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          <div className="flex space-x-2">
            <Button variant="ghost" onClick={onCancel}>
              Cancel
            </Button>
            <Button onClick={onNext} disabled={selectedFields.length === 0}>
              Next: Preview Export
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
