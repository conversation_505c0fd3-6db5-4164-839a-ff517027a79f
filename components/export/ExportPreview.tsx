/**
 * ExportPreview Component for Disc Golf Inventory Management System
 *
 * A component for previewing export settings and initiating the download
 * with progress indication for large collections.
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Download, FileText, Table, Check, Loader2, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Disc } from "@/lib/types";
import type { ExportFormat } from "@/lib/exportImport";
// Import will be used when implementing actual export functionality
// import { exportAndDownload, exportCollection } from "@/lib/exportImport";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * ExportPreview component props
 */
export interface ExportPreviewProps {
  /** Array of discs to export */
  discs: Disc[];
  /** Selected export format */
  format: ExportFormat;
  /** Selected fields for export */
  selectedFields: string[];
  /** Export filename */
  filename: string;
  /** Whether to include metadata */
  includeMetadata: boolean;
  /** Whether to use pretty formatting */
  prettyFormat: boolean;
  /** Whether export is in progress */
  isExporting: boolean;
  /** Export error if any */
  error: string | null;
  /** Navigation callbacks */
  onExport: () => void;
  onPrevious: () => void;
  onCancel: () => void;
  /** Additional CSS classes */
  className?: string;
}

// ============================================================================
// COMPONENTS
// ============================================================================

/**
 * Export summary card
 */
function ExportSummaryCard({
  format,
  discCount,
  fieldCount,
  filename,
  includeMetadata,
  prettyFormat,
}: {
  format: ExportFormat;
  discCount: number;
  fieldCount: number;
  filename: string;
  includeMetadata: boolean;
  prettyFormat: boolean;
}) {
  const Icon = format === "json" ? FileText : Table;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Icon className="h-5 w-5" />
          <span>Export Summary</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-sm text-muted-foreground">Format</div>
            <div className="font-medium">{format.toUpperCase()}</div>
          </div>
          <div>
            <div className="text-sm text-muted-foreground">Filename</div>
            <div className="font-medium">{filename}</div>
          </div>
          <div>
            <div className="text-sm text-muted-foreground">Discs</div>
            <div className="font-medium">{discCount} items</div>
          </div>
          <div>
            <div className="text-sm text-muted-foreground">Fields</div>
            <div className="font-medium">{fieldCount} selected</div>
          </div>
        </div>

        {format === "json" && (
          <div className="space-y-2 pt-2 border-t">
            <div className="text-sm font-medium">JSON Options</div>
            <div className="space-y-1">
              <div className="flex items-center space-x-2 text-sm">
                {includeMetadata ? <Check className="h-4 w-4 text-green-600" /> : <div className="h-4 w-4" />}
                <span className={includeMetadata ? "text-foreground" : "text-muted-foreground"}>Include metadata</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                {prettyFormat ? <Check className="h-4 w-4 text-green-600" /> : <div className="h-4 w-4" />}
                <span className={prettyFormat ? "text-foreground" : "text-muted-foreground"}>Pretty formatting</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Data preview card
 */
function DataPreviewCard({
  discs,
  selectedFields,
  format,
}: {
  discs: Disc[];
  selectedFields: string[];
  format: ExportFormat;
}) {
  const previewData = React.useMemo(() => {
    if (discs.length === 0) return "";

    try {
      // Create a preview with first 3 discs
      const previewDiscs = discs.slice(0, 3);

      if (format === "json") {
        // Filter fields for preview
        const filteredDiscs = previewDiscs.map((disc) => {
          const filtered: Record<string, unknown> = {};
          selectedFields.forEach((field) => {
            if (field.includes(".")) {
              // Handle nested fields like flightNumbers.speed
              const [parent, child] = field.split(".");
              if (!filtered[parent]) filtered[parent] = {};
              (filtered[parent] as Record<string, unknown>)[child] = (disc as Record<string, unknown>)[parent]?.[
                child as keyof (typeof disc)[keyof typeof disc]
              ];
            } else {
              filtered[field] = (disc as Record<string, unknown>)[field];
            }
          });
          return filtered;
        });

        return JSON.stringify(filteredDiscs, null, 2);
      } else {
        // CSV preview - just show headers and first row
        const headers = selectedFields.join(",");
        const firstRow = selectedFields
          .map((field) => {
            if (field.includes(".")) {
              const [parent, child] = field.split(".");
              return (
                (previewDiscs[0] as Record<string, unknown>)[parent]?.[
                  child as keyof (typeof previewDiscs)[0][keyof (typeof previewDiscs)[0]]
                ] || ""
              );
            }
            return (previewDiscs[0] as Record<string, unknown>)[field] || "";
          })
          .join(",");

        return `${headers}\n${firstRow}\n...`;
      }
    } catch {
      return "Error generating preview";
    }
  }, [discs, selectedFields, format]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Data Preview</CardTitle>
        <CardDescription>
          Preview of the first {Math.min(3, discs.length)} disc{discs.length !== 1 ? "s" : ""} in your export
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="bg-muted/50 rounded-lg p-4 overflow-auto max-h-64">
          <pre className="text-xs text-muted-foreground whitespace-pre-wrap">{previewData}</pre>
        </div>
        {discs.length > 3 && (
          <p className="text-xs text-muted-foreground mt-2">... and {discs.length - 3} more discs</p>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Export progress indicator
 */
function ExportProgress({ isExporting, error }: { isExporting: boolean; error: string | null }) {
  if (!isExporting && !error) return null;

  return (
    <Card className={cn("border-l-4", error ? "border-l-destructive" : "border-l-primary")}>
      <CardContent className="py-4">
        <div className="flex items-center space-x-3">
          {isExporting && (
            <>
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
              <div>
                <div className="font-medium">Generating export...</div>
                <div className="text-sm text-muted-foreground">Please wait while we prepare your file</div>
              </div>
            </>
          )}
          {error && (
            <>
              <AlertCircle className="h-5 w-5 text-destructive" />
              <div>
                <div className="font-medium text-destructive">Export failed</div>
                <div className="text-sm text-muted-foreground">{error}</div>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * ExportPreview component for reviewing export settings and initiating download
 */
export function ExportPreview({
  discs,
  format,
  selectedFields,
  filename,
  includeMetadata,
  prettyFormat,
  isExporting,
  error,
  onExport,
  onPrevious,
  onCancel,
  className,
}: ExportPreviewProps) {
  const estimatedSize = React.useMemo(() => {
    // Rough estimation of file size
    const avgFieldSize = 20; // Average bytes per field
    const totalFields = selectedFields.length * discs.length;
    const baseSize = totalFields * avgFieldSize;

    // Add overhead for JSON structure or CSV headers
    const overhead = format === "json" ? baseSize * 0.3 : selectedFields.length * 10;
    const totalBytes = baseSize + overhead;

    if (totalBytes < 1024) return `${totalBytes} B`;
    if (totalBytes < 1024 * 1024) return `${(totalBytes / 1024).toFixed(1)} KB`;
    return `${(totalBytes / (1024 * 1024)).toFixed(1)} MB`;
  }, [discs.length, selectedFields.length, format]);

  return (
    <Card className={cn("max-w-4xl mx-auto", className)}>
      <CardHeader className="text-center">
        <CardTitle className="text-2xl">Preview Export</CardTitle>
        <CardDescription>Review your export settings and download your file</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Export Progress */}
        <ExportProgress isExporting={isExporting} error={error} />

        {/* Export Summary */}
        <ExportSummaryCard
          format={format}
          discCount={discs.length}
          fieldCount={selectedFields.length}
          filename={filename}
          includeMetadata={includeMetadata}
          prettyFormat={prettyFormat}
        />

        {/* Data Preview */}
        <DataPreviewCard discs={discs} selectedFields={selectedFields} format={format} />

        {/* File Size Estimate */}
        <div className="text-center text-sm text-muted-foreground">
          Estimated file size: <span className="font-medium">{estimatedSize}</span>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6 border-t">
          <Button variant="outline" onClick={onPrevious} disabled={isExporting}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>
          <div className="flex space-x-2">
            <Button variant="ghost" onClick={onCancel} disabled={isExporting}>
              Cancel
            </Button>
            <Button onClick={onExport} disabled={isExporting || selectedFields.length === 0}>
              {isExporting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Download Export
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
