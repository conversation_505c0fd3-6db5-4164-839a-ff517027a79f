/**
 * ExportWizard Component for Disc Golf Inventory Management System
 *
 * A multi-step wizard component for guiding users through the export process
 * with step navigation, state management, and progress indication.
 */

"use client";

import * as React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Download, FileText, Settings, Eye } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Disc } from "@/lib/types";
import type { ExportFormat } from "@/lib/exportImport";
import { exportAndDownload, generateExportFilename } from "@/lib/exportImport";
import { FormatSelector } from "./FormatSelector";
import { FieldSelector } from "./FieldSelector";
import { ExportPreview } from "./ExportPreview";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Export wizard step definitions
 */
export type ExportStep = "format" | "fields" | "preview" | "download";

/**
 * Export wizard state
 */
export interface ExportWizardState {
  currentStep: ExportStep;
  format: ExportFormat;
  selectedFields: string[];
  includeMetadata: boolean;
  prettyFormat: boolean;
  filename: string;
  isExporting: boolean;
  error: string | null;
}

/**
 * Step configuration for the wizard
 */
interface StepConfig {
  key: ExportStep;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

/**
 * Export wizard props
 */
export interface ExportWizardProps {
  /** Array of discs to export */
  discs: Disc[];
  /** Initial wizard state */
  initialState?: Partial<ExportWizardState>;
  /** Callback when export is completed */
  onExportComplete?: (data: string, filename: string) => void;
  /** Callback when wizard is cancelled */
  onCancel?: () => void;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Step component props
 */
interface StepComponentProps {
  discs: Disc[];
  state: ExportWizardState;
  onStateChange: (updates: Partial<ExportWizardState>) => void;
  onNext: () => void;
  onPrevious: () => void;
  onCancel: () => void;
}

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * Wizard step configurations
 */
const WIZARD_STEPS: StepConfig[] = [
  {
    key: "format",
    title: "Choose Format",
    description: "Select export format and options",
    icon: FileText,
  },
  {
    key: "fields",
    title: "Select Fields",
    description: "Choose which data to include",
    icon: Settings,
  },
  {
    key: "preview",
    title: "Preview Export",
    description: "Review your export settings",
    icon: Eye,
  },
  {
    key: "download",
    title: "Download",
    description: "Generate and download file",
    icon: Download,
  },
];

/**
 * All available fields for export
 */
const ALL_FIELDS = [
  "id",
  "manufacturer",
  "mold",
  "plasticType",
  "weight",
  "color",
  "flightNumbers.speed",
  "flightNumbers.glide",
  "flightNumbers.turn",
  "flightNumbers.fade",
  "condition",
  "currentLocation",
  "purchaseDate",
  "purchasePrice",
  "notes",
  "imageUrl",
  "createdAt",
  "updatedAt",
];

/**
 * Default export state
 */
const DEFAULT_EXPORT_STATE: ExportWizardState = {
  currentStep: "format",
  format: "json",
  selectedFields: ALL_FIELDS,
  includeMetadata: true,
  prettyFormat: true,
  filename: "",
  isExporting: false,
  error: null,
};

// ============================================================================
// COMPONENTS
// ============================================================================

/**
 * Progress indicator for the export wizard
 */
function ExportProgressIndicator({ currentStep, className }: { currentStep: ExportStep; className?: string }) {
  const getCurrentStepIndex = () => {
    return WIZARD_STEPS.findIndex((step) => step.key === currentStep);
  };

  const currentStepIndex = getCurrentStepIndex();

  return (
    <div className={cn("flex items-center justify-center space-x-4", className)}>
      {WIZARD_STEPS.map((step, index) => {
        const isActive = index === currentStepIndex;
        const isCompleted = index < currentStepIndex;
        const Icon = step.icon;

        return (
          <div key={step.key} className="flex items-center">
            {/* Step Circle */}
            <div
              className={cn(
                "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors",
                isActive && "border-primary bg-primary text-primary-foreground",
                isCompleted && "border-green-500 bg-green-500 text-white",
                !isActive && !isCompleted && "border-muted-foreground text-muted-foreground"
              )}
            >
              <Icon className="h-4 w-4" />
            </div>

            {/* Step Label */}
            <div className="ml-3 hidden sm:block">
              <div
                className={cn(
                  "text-sm font-medium",
                  isActive && "text-foreground",
                  isCompleted && "text-green-600",
                  !isActive && !isCompleted && "text-muted-foreground"
                )}
              >
                {step.title}
              </div>
              <div className="text-xs text-muted-foreground">{step.description}</div>
            </div>

            {/* Connector Line */}
            {index < WIZARD_STEPS.length - 1 && (
              <div
                className={cn(
                  "w-12 h-0.5 mx-4 transition-colors",
                  isCompleted && "bg-green-500",
                  !isCompleted && "bg-muted"
                )}
              />
            )}
          </div>
        );
      })}
    </div>
  );
}

/**
 * Render the appropriate step component based on current step
 */
function WizardStepRenderer({ discs, state, onStateChange, onNext, onPrevious, onCancel }: StepComponentProps) {
  switch (state.currentStep) {
    case "format":
      return (
        <FormatSelector
          selectedFormat={state.format}
          onFormatChange={(format) => onStateChange({ format })}
          filename={state.filename}
          onFilenameChange={(filename) => onStateChange({ filename })}
          includeMetadata={state.includeMetadata}
          onIncludeMetadataChange={(includeMetadata) => onStateChange({ includeMetadata })}
          prettyFormat={state.prettyFormat}
          onPrettyFormatChange={(prettyFormat) => onStateChange({ prettyFormat })}
          discCount={discs.length}
          onNext={onNext}
          onPrevious={onPrevious}
          onCancel={onCancel}
        />
      );

    case "fields":
      return (
        <FieldSelector
          selectedFields={state.selectedFields}
          onFieldSelectionChange={(selectedFields) => onStateChange({ selectedFields })}
          onNext={onNext}
          onPrevious={onPrevious}
          onCancel={onCancel}
        />
      );

    case "preview":
    case "download":
      return (
        <ExportPreview
          discs={discs}
          format={state.format}
          selectedFields={state.selectedFields}
          filename={state.filename}
          includeMetadata={state.includeMetadata}
          prettyFormat={state.prettyFormat}
          isExporting={state.isExporting}
          error={state.error}
          onExport={onNext}
          onPrevious={onPrevious}
          onCancel={onCancel}
        />
      );

    default:
      return (
        <Card className="max-w-2xl mx-auto">
          <CardContent className="py-12 text-center">
            <p className="text-muted-foreground">Unknown step: {state.currentStep}</p>
          </CardContent>
        </Card>
      );
  }
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * ExportWizard component for multi-step export process
 */
export function ExportWizard({ discs, initialState = {}, onExportComplete, onCancel, className }: ExportWizardProps) {
  const [wizardState, setWizardState] = React.useState<ExportWizardState>({
    ...DEFAULT_EXPORT_STATE,
    ...initialState,
  });

  // Update state helper
  const updateState = React.useCallback((updates: Partial<ExportWizardState>) => {
    setWizardState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Initialize filename when format changes
  React.useEffect(() => {
    if (!wizardState.filename) {
      updateState({ filename: generateExportFilename(wizardState.format) });
    }
  }, [wizardState.format, wizardState.filename, updateState]);

  // Navigation handlers
  const handleNext = React.useCallback(() => {
    const currentIndex = WIZARD_STEPS.findIndex((step) => step.key === wizardState.currentStep);
    if (currentIndex < WIZARD_STEPS.length - 1) {
      const nextStep = WIZARD_STEPS[currentIndex + 1];
      updateState({ currentStep: nextStep.key });
    } else if (wizardState.currentStep === "download" || wizardState.currentStep === "preview") {
      // Handle export logic
      updateState({ isExporting: true, error: null });

      try {
        const exportOptions = {
          format: wizardState.format,
          filename: wizardState.filename,
          includeMetadata: wizardState.includeMetadata,
          prettyFormat: wizardState.prettyFormat,
        };

        // Filter discs to only include selected fields
        const filteredDiscs = discs.map((disc) => {
          const filtered: Record<string, unknown> = {};
          wizardState.selectedFields.forEach((field) => {
            if (field.includes(".")) {
              // Handle nested fields like flightNumbers.speed
              const [parent, child] = field.split(".");
              if (!filtered[parent]) filtered[parent] = {};
              (filtered[parent] as Record<string, unknown>)[child] = (disc as Record<string, unknown>)[parent]?.[
                child as keyof (typeof disc)[keyof typeof disc]
              ];
            } else {
              filtered[field] = (disc as Record<string, unknown>)[field];
            }
          });
          return filtered;
        });

        exportAndDownload(filteredDiscs, exportOptions);

        updateState({ isExporting: false });
        onExportComplete?.(`Exported ${discs.length} discs`, wizardState.filename);
      } catch (error) {
        updateState({
          isExporting: false,
          error: error instanceof Error ? error.message : "Export failed",
        });
      }
    }
  }, [wizardState, updateState, onExportComplete, discs]);

  const handlePrevious = React.useCallback(() => {
    const currentIndex = WIZARD_STEPS.findIndex((step) => step.key === wizardState.currentStep);
    if (currentIndex > 0) {
      const previousStep = WIZARD_STEPS[currentIndex - 1];
      updateState({ currentStep: previousStep.key });
    }
  }, [wizardState.currentStep, updateState]);

  const handleCancel = React.useCallback(() => {
    onCancel?.();
  }, [onCancel]);

  return (
    <div className={cn("space-y-8", className)}>
      {/* Progress Indicator */}
      <ExportProgressIndicator currentStep={wizardState.currentStep} />

      {/* Wizard Step Content */}
      <WizardStepRenderer
        discs={discs}
        state={wizardState}
        onStateChange={updateState}
        onNext={handleNext}
        onPrevious={handlePrevious}
        onCancel={handleCancel}
      />
    </div>
  );
}
