/**
 * FormatSelector Component for Disc Golf Inventory Management System
 *
 * A component for selecting export format (JSON/CSV) with format descriptions,
 * icons, and integration with wizard state.
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { FileText, Table, Check, ArrowRight, ArrowLeft } from "lucide-react";
import { cn } from "@/lib/utils";
import type { ExportFormat } from "@/lib/exportImport";
import { generateExportFilename } from "@/lib/exportImport";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Format option configuration
 */
interface FormatOption {
  value: ExportFormat;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  features: string[];
}

/**
 * FormatSelector component props
 */
export interface FormatSelectorProps {
  /** Currently selected format */
  selectedFormat: ExportFormat;
  /** Callback when format changes */
  onFormatChange: (format: ExportFormat) => void;
  /** Current filename */
  filename: string;
  /** Callback when filename changes */
  onFilenameChange: (filename: string) => void;
  /** Whether to include metadata */
  includeMetadata: boolean;
  /** Callback when metadata option changes */
  onIncludeMetadataChange: (include: boolean) => void;
  /** Whether to use pretty formatting */
  prettyFormat: boolean;
  /** Callback when pretty format option changes */
  onPrettyFormatChange: (pretty: boolean) => void;
  /** Number of discs being exported */
  discCount: number;
  /** Navigation callbacks */
  onNext: () => void;
  onPrevious?: () => void;
  onCancel: () => void;
  /** Additional CSS classes */
  className?: string;
}

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * Available export format options
 */
const FORMAT_OPTIONS: FormatOption[] = [
  {
    value: "json",
    label: "JSON",
    description: "JavaScript Object Notation - Complete data with metadata",
    icon: FileText,
    features: [
      "Complete data structure",
      "Includes all fields and metadata",
      "Easy to import back",
      "Human-readable with pretty formatting",
      "Preserves data types"
    ]
  },
  {
    value: "csv",
    label: "CSV",
    description: "Comma-Separated Values - Spreadsheet compatible format",
    icon: Table,
    features: [
      "Spreadsheet compatible",
      "Works with Excel, Google Sheets",
      "Flat data structure",
      "Easy to analyze and filter",
      "Universal format support"
    ]
  }
];

// ============================================================================
// COMPONENTS
// ============================================================================

/**
 * Format option card component
 */
function FormatOptionCard({
  option,
  isSelected,
  onSelect,
  className
}: {
  option: FormatOption;
  isSelected: boolean;
  onSelect: () => void;
  className?: string;
}) {
  const Icon = option.icon;

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all hover:shadow-md",
        isSelected && "ring-2 ring-primary border-primary",
        className
      )}
      onClick={onSelect}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center space-x-3">
          <div
            className={cn(
              "flex items-center justify-center w-10 h-10 rounded-full",
              isSelected ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
            )}
          >
            <Icon className="h-5 w-5" />
          </div>
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center space-x-2">
              <span>{option.label}</span>
              {isSelected && <Check className="h-4 w-4 text-primary" />}
            </CardTitle>
            <CardDescription className="text-sm">
              {option.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <ul className="space-y-1">
          {option.features.map((feature, index) => (
            <li key={index} className="text-sm text-muted-foreground flex items-center space-x-2">
              <div className="w-1 h-1 bg-muted-foreground rounded-full" />
              <span>{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}

/**
 * Export options section
 */
function ExportOptions({
  format,
  includeMetadata,
  onIncludeMetadataChange,
  prettyFormat,
  onPrettyFormatChange
}: {
  format: ExportFormat;
  includeMetadata: boolean;
  onIncludeMetadataChange: (include: boolean) => void;
  prettyFormat: boolean;
  onPrettyFormatChange: (pretty: boolean) => void;
}) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Export Options</h3>
      
      {format === "json" && (
        <>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="includeMetadata"
              checked={includeMetadata}
              onChange={(e) => onIncludeMetadataChange(e.target.checked)}
              className="rounded border-gray-300"
            />
            <Label htmlFor="includeMetadata" className="text-sm">
              Include metadata (export date, version, disc count)
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="prettyFormat"
              checked={prettyFormat}
              onChange={(e) => onPrettyFormatChange(e.target.checked)}
              className="rounded border-gray-300"
            />
            <Label htmlFor="prettyFormat" className="text-sm">
              Pretty formatting (human-readable with indentation)
            </Label>
          </div>
        </>
      )}
      
      {format === "csv" && (
        <div className="text-sm text-muted-foreground">
          <p>CSV exports include all disc fields in a flat structure compatible with spreadsheet applications.</p>
        </div>
      )}
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * FormatSelector component for choosing export format and options
 */
export function FormatSelector({
  selectedFormat,
  onFormatChange,
  filename,
  onFilenameChange,
  includeMetadata,
  onIncludeMetadataChange,
  prettyFormat,
  onPrettyFormatChange,
  discCount,
  onNext,
  onPrevious,
  onCancel,
  className
}: FormatSelectorProps) {
  // Auto-update filename when format changes
  React.useEffect(() => {
    if (!filename || filename === generateExportFilename(selectedFormat === "json" ? "csv" : "json")) {
      onFilenameChange(generateExportFilename(selectedFormat));
    }
  }, [selectedFormat, filename, onFilenameChange]);

  return (
    <Card className={cn("max-w-4xl mx-auto", className)}>
      <CardHeader className="text-center">
        <CardTitle className="text-2xl">Choose Export Format</CardTitle>
        <CardDescription>
          Select the format for exporting your {discCount} discs
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Format Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {FORMAT_OPTIONS.map((option) => (
            <FormatOptionCard
              key={option.value}
              option={option}
              isSelected={selectedFormat === option.value}
              onSelect={() => onFormatChange(option.value)}
            />
          ))}
        </div>

        {/* Export Options */}
        <ExportOptions
          format={selectedFormat}
          includeMetadata={includeMetadata}
          onIncludeMetadataChange={onIncludeMetadataChange}
          prettyFormat={prettyFormat}
          onPrettyFormatChange={onPrettyFormatChange}
        />

        {/* Filename Input */}
        <div className="space-y-2">
          <Label htmlFor="filename" className="text-sm font-medium">
            Filename
          </Label>
          <Input
            id="filename"
            value={filename}
            onChange={(e) => onFilenameChange(e.target.value)}
            placeholder={generateExportFilename(selectedFormat)}
            className="max-w-md"
          />
          <p className="text-xs text-muted-foreground">
            The file will be downloaded with this name
          </p>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6 border-t">
          <div>
            {onPrevious && (
              <Button variant="outline" onClick={onPrevious}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
            )}
          </div>
          <div className="flex space-x-2">
            <Button variant="ghost" onClick={onCancel}>
              Cancel
            </Button>
            <Button onClick={onNext}>
              Next: Select Fields
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
