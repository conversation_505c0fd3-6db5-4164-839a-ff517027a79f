/**
 * useInventory Hook for the Disc Golf Inventory Management System
 *
 * This hook provides comprehensive disc collection state management with CRUD operations,
 * optimistic updates, error handling with rollback capabilities, and localStorage persistence.
 */

"use client";

import { useState, useCallback, useMemo } from "react";
import { useLocalStorage } from "./useLocalStorage";
import { STORAGE_KEYS } from "@/lib/storage";
import { validateDisc, CreateDiscSchema } from "@/lib/validation";
import type { Disc } from "@/lib/types";
import { DiscCondition, Location } from "@/lib/types";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Error types for inventory operations
 */
export enum InventoryErrorType {
  VALIDATION_ERROR = "validation_error",
  STORAGE_ERROR = "storage_error",
  NOT_FOUND = "not_found",
  DUPLICATE_ID = "duplicate_id",
  OPERATION_FAILED = "operation_failed",
}

/**
 * Inventory operation error
 */
export class InventoryError extends Error {
  constructor(public type: InventoryErrorType, message: string, public details?: unknown) {
    super(message);
    this.name = "InventoryError";
  }
}

/**
 * Result type for inventory operations
 */
export interface InventoryOperationResult<T = void> {
  success: boolean;
  data?: T;
  error?: InventoryError;
}

/**
 * Disc creation input (without auto-generated fields)
 */
export type CreateDiscInput = Omit<Disc, "id" | "createdAt" | "updatedAt">;

/**
 * Disc update input (partial fields except id)
 */
export type UpdateDiscInput = Partial<Omit<Disc, "id" | "createdAt">> & {
  id: string;
};

/**
 * Collection statistics
 */
export interface CollectionStats {
  totalDiscs: number;
  byCondition: Record<DiscCondition, number>;
  byLocation: Record<Location, number>;
  byManufacturer: Record<string, number>;
  totalValue: number;
  averageWeight: number;
}

/**
 * Hook return type
 */
export interface UseInventoryReturn {
  // State
  discs: Disc[];
  loading: boolean;
  error: InventoryError | null;

  // CRUD Operations
  addDisc: (discData: CreateDiscInput) => Promise<InventoryOperationResult<Disc>>;
  updateDisc: (discData: UpdateDiscInput) => Promise<InventoryOperationResult<Disc>>;
  removeDisc: (discId: string) => Promise<InventoryOperationResult>;
  getDisc: (discId: string) => Disc | undefined;

  // Bulk Operations
  addMultipleDiscs: (discsData: CreateDiscInput[]) => Promise<InventoryOperationResult<Disc[]>>;
  removeMultipleDiscs: (discIds: string[]) => Promise<InventoryOperationResult>;
  clearCollection: () => Promise<InventoryOperationResult>;
  replaceCollection: (discs: Disc[]) => Promise<InventoryOperationResult<Disc[]>>;

  // Utility Functions
  getCollectionStats: () => CollectionStats;
  exportCollection: () => string;
  importCollection: (jsonData: string) => Promise<InventoryOperationResult<Disc[]>>;

  // State Management
  refresh: () => void;
  clearError: () => void;
}

// ============================================================================
// MAIN HOOK
// ============================================================================

/**
 * Custom hook for managing disc golf inventory
 *
 * Features:
 * - CRUD operations with optimistic updates
 * - Error handling with automatic rollback
 * - localStorage persistence via useLocalStorage hook
 * - Collection statistics and analytics
 * - Data export/import functionality
 * - TypeScript type safety with validation
 *
 * @returns Hook state and methods
 */
export function useInventory(): UseInventoryReturn {
  const {
    value: discs = [],
    setValue: setDiscs,
    loading: storageLoading,
    error: storageError,
  } = useLocalStorage<Disc[]>(STORAGE_KEYS.DISCS, {
    defaultValue: [],
    syncAcrossTabs: true,
  });

  const [operationLoading, setOperationLoading] = useState(false);
  const [operationError, setOperationError] = useState<InventoryError | null>(null);

  // Combined loading state
  const loading = storageLoading || operationLoading;

  // Combined error state
  const error = useMemo(() => {
    if (operationError) return operationError;
    if (storageError) {
      return new InventoryError(InventoryErrorType.STORAGE_ERROR, "Storage operation failed", storageError);
    }
    return null;
  }, [operationError, storageError]);

  /**
   * Clear operation error
   */
  const clearError = useCallback(() => {
    setOperationError(null);
  }, []);

  /**
   * Refresh collection from storage
   */
  const refresh = useCallback(() => {
    // The useLocalStorage hook handles refresh automatically
    clearError();
  }, [clearError]);

  /**
   * Create a new disc with auto-generated fields
   */
  const createDiscWithMetadata = useCallback((discData: CreateDiscInput): Disc => {
    const now = new Date();
    return {
      ...discData,
      id: crypto.randomUUID(),
      createdAt: now,
      updatedAt: now,
    };
  }, []);

  /**
   * Validate disc data and handle errors
   */
  const validateDiscData = useCallback((disc: unknown): InventoryOperationResult<Disc> => {
    const validation = validateDisc(disc);
    if (!validation.isValid) {
      const error = new InventoryError(
        InventoryErrorType.VALIDATION_ERROR,
        "Disc validation failed",
        validation.errors
      );
      return { success: false, error };
    }
    return { success: true, data: disc as Disc };
  }, []);

  /**
   * Get a disc by ID
   */
  const getDisc = useCallback(
    (discId: string): Disc | undefined => {
      return discs.find((disc) => disc.id === discId);
    },
    [discs]
  );

  /**
   * Add a new disc to the collection
   */
  const addDisc = useCallback(
    async (discData: CreateDiscInput): Promise<InventoryOperationResult<Disc>> => {
      try {
        setOperationLoading(true);
        setOperationError(null);

        // Validate input data
        const inputValidation = CreateDiscSchema.safeParse(discData);
        if (!inputValidation.success) {
          const error = new InventoryError(
            InventoryErrorType.VALIDATION_ERROR,
            "Invalid disc data",
            inputValidation.error.issues
          );
          return { success: false, error };
        }

        // Create disc with metadata
        const newDisc = createDiscWithMetadata(discData);

        // Validate complete disc
        const validation = validateDiscData(newDisc);
        if (!validation.success) {
          return validation;
        }

        // Optimistic update
        const updatedDiscs = [...discs, newDisc];
        setDiscs(updatedDiscs);

        return { success: true, data: newDisc };
      } catch (err) {
        const error = new InventoryError(InventoryErrorType.OPERATION_FAILED, "Failed to add disc", err);
        setOperationError(error);
        return { success: false, error };
      } finally {
        setOperationLoading(false);
      }
    },
    [discs, setDiscs, createDiscWithMetadata, validateDiscData]
  );

  /**
   * Update an existing disc
   */
  const updateDisc = useCallback(
    async (discData: UpdateDiscInput): Promise<InventoryOperationResult<Disc>> => {
      try {
        setOperationLoading(true);
        setOperationError(null);

        // Find existing disc
        const existingDisc = getDisc(discData.id);
        if (!existingDisc) {
          const error = new InventoryError(InventoryErrorType.NOT_FOUND, `Disc with ID ${discData.id} not found`);
          return { success: false, error };
        }

        // Create updated disc
        const updatedDisc: Disc = {
          ...existingDisc,
          ...discData,
          updatedAt: new Date(),
        };

        // Validate updated disc
        const validation = validateDiscData(updatedDisc);
        if (!validation.success) {
          return validation;
        }

        // Optimistic update with rollback capability
        const previousDiscs = discs;
        const updatedDiscs = discs.map((disc) => (disc.id === discData.id ? updatedDisc : disc));

        try {
          setDiscs(updatedDiscs);
          return { success: true, data: updatedDisc };
        } catch (storageErr) {
          // Rollback on storage failure
          setDiscs(previousDiscs);
          throw storageErr;
        }
      } catch (err) {
        const error = new InventoryError(InventoryErrorType.OPERATION_FAILED, "Failed to update disc", err);
        setOperationError(error);
        return { success: false, error };
      } finally {
        setOperationLoading(false);
      }
    },
    [discs, setDiscs, getDisc, validateDiscData]
  );

  /**
   * Remove a disc from the collection
   */
  const removeDisc = useCallback(
    async (discId: string): Promise<InventoryOperationResult> => {
      try {
        setOperationLoading(true);
        setOperationError(null);

        // Check if disc exists
        const existingDisc = getDisc(discId);
        if (!existingDisc) {
          const error = new InventoryError(InventoryErrorType.NOT_FOUND, `Disc with ID ${discId} not found`);
          return { success: false, error };
        }

        // Optimistic update with rollback capability
        const previousDiscs = discs;
        const updatedDiscs = discs.filter((disc) => disc.id !== discId);

        try {
          setDiscs(updatedDiscs);
          return { success: true };
        } catch (storageErr) {
          // Rollback on storage failure
          setDiscs(previousDiscs);
          throw storageErr;
        }
      } catch (err) {
        const error = new InventoryError(InventoryErrorType.OPERATION_FAILED, "Failed to remove disc", err);
        setOperationError(error);
        return { success: false, error };
      } finally {
        setOperationLoading(false);
      }
    },
    [discs, setDiscs, getDisc]
  );

  /**
   * Add multiple discs to the collection
   */
  const addMultipleDiscs = useCallback(
    async (discsData: CreateDiscInput[]): Promise<InventoryOperationResult<Disc[]>> => {
      try {
        setOperationLoading(true);
        setOperationError(null);

        // Validate all input data
        const validationResults = discsData.map((discData) => CreateDiscSchema.safeParse(discData));
        const invalidResults = validationResults.filter((result) => !result.success);

        if (invalidResults.length > 0) {
          const error = new InventoryError(
            InventoryErrorType.VALIDATION_ERROR,
            `${invalidResults.length} discs have invalid data`,
            invalidResults.map((result) => result.error?.issues).filter(Boolean)
          );
          return { success: false, error };
        }

        // Create discs with metadata
        const newDiscs = discsData.map((discData) => createDiscWithMetadata(discData));

        // Validate all complete discs
        const discValidations = newDiscs.map((disc) => validateDiscData(disc));
        const invalidDiscs = discValidations.filter((validation) => !validation.success);

        if (invalidDiscs.length > 0) {
          const error = new InventoryError(
            InventoryErrorType.VALIDATION_ERROR,
            `${invalidDiscs.length} discs failed validation`,
            invalidDiscs.map((validation) => validation.error).filter(Boolean)
          );
          return { success: false, error };
        }

        // Optimistic update
        const updatedDiscs = [...discs, ...newDiscs];
        setDiscs(updatedDiscs);

        return { success: true, data: newDiscs };
      } catch (err) {
        const error = new InventoryError(InventoryErrorType.OPERATION_FAILED, "Failed to add multiple discs", err);
        setOperationError(error);
        return { success: false, error };
      } finally {
        setOperationLoading(false);
      }
    },
    [discs, setDiscs, createDiscWithMetadata, validateDiscData]
  );

  /**
   * Remove multiple discs from the collection
   */
  const removeMultipleDiscs = useCallback(
    async (discIds: string[]): Promise<InventoryOperationResult> => {
      try {
        setOperationLoading(true);
        setOperationError(null);

        // Check if all discs exist
        const missingIds = discIds.filter((id) => !getDisc(id));
        if (missingIds.length > 0) {
          const error = new InventoryError(
            InventoryErrorType.NOT_FOUND,
            `${missingIds.length} discs not found`,
            missingIds
          );
          return { success: false, error };
        }

        // Optimistic update with rollback capability
        const previousDiscs = discs;
        const updatedDiscs = discs.filter((disc) => !discIds.includes(disc.id));

        try {
          setDiscs(updatedDiscs);
          return { success: true };
        } catch (storageErr) {
          // Rollback on storage failure
          setDiscs(previousDiscs);
          throw storageErr;
        }
      } catch (err) {
        const error = new InventoryError(InventoryErrorType.OPERATION_FAILED, "Failed to remove multiple discs", err);
        setOperationError(error);
        return { success: false, error };
      } finally {
        setOperationLoading(false);
      }
    },
    [discs, setDiscs, getDisc]
  );

  /**
   * Clear the entire collection
   */
  const clearCollection = useCallback(async (): Promise<InventoryOperationResult> => {
    try {
      setOperationLoading(true);
      setOperationError(null);

      // Optimistic update with rollback capability
      const previousDiscs = discs;

      try {
        setDiscs([]);
        return { success: true };
      } catch (storageErr) {
        // Rollback on storage failure
        setDiscs(previousDiscs);
        throw storageErr;
      }
    } catch (err) {
      const error = new InventoryError(InventoryErrorType.OPERATION_FAILED, "Failed to clear collection", err);
      setOperationError(error);
      return { success: false, error };
    } finally {
      setOperationLoading(false);
    }
  }, [discs, setDiscs]);

  /**
   * Replace the entire collection with new discs
   */
  const replaceCollection = useCallback(
    async (newDiscs: Disc[]): Promise<InventoryOperationResult<Disc[]>> => {
      try {
        setOperationLoading(true);
        setOperationError(null);

        // Validate all discs
        const validationResults = newDiscs.map((disc) => validateDiscData(disc));
        const failedValidations = validationResults.filter((result) => !result.success);

        if (failedValidations.length > 0) {
          const error = new InventoryError(
            InventoryErrorType.VALIDATION_ERROR,
            "Some discs failed validation",
            failedValidations.map((result) => result.error).filter(Boolean)
          );
          return { success: false, error };
        }

        // Optimistic update with rollback capability
        const previousDiscs = discs;

        try {
          setDiscs(newDiscs);
          return { success: true, data: newDiscs };
        } catch (storageErr) {
          // Rollback on storage failure
          setDiscs(previousDiscs);
          throw storageErr;
        }
      } catch (err) {
        const error = new InventoryError(InventoryErrorType.OPERATION_FAILED, "Failed to replace collection", err);
        setOperationError(error);
        return { success: false, error };
      } finally {
        setOperationLoading(false);
      }
    },
    [discs, setDiscs, validateDiscData]
  );

  /**
   * Calculate collection statistics
   */
  const getCollectionStats = useCallback((): CollectionStats => {
    const stats: CollectionStats = {
      totalDiscs: discs.length,
      byCondition: {} as Record<DiscCondition, number>,
      byLocation: {} as Record<Location, number>,
      byManufacturer: {},
      totalValue: 0,
      averageWeight: 0,
    };

    // Initialize condition counts
    Object.values(DiscCondition).forEach((condition) => {
      stats.byCondition[condition] = 0;
    });

    // Initialize location counts
    Object.values(Location).forEach((location) => {
      stats.byLocation[location] = 0;
    });

    if (discs.length === 0) {
      return stats;
    }

    let totalWeight = 0;

    discs.forEach((disc) => {
      // Count by condition
      stats.byCondition[disc.condition]++;

      // Count by location
      stats.byLocation[disc.currentLocation]++;

      // Count by manufacturer
      stats.byManufacturer[disc.manufacturer] = (stats.byManufacturer[disc.manufacturer] || 0) + 1;

      // Sum purchase prices
      if (disc.purchasePrice) {
        stats.totalValue += disc.purchasePrice;
      }

      // Sum weights
      totalWeight += disc.weight;
    });

    // Calculate average weight
    stats.averageWeight = totalWeight / discs.length;

    return stats;
  }, [discs]);

  /**
   * Export collection as JSON string
   */
  const exportCollection = useCallback((): string => {
    try {
      return JSON.stringify(discs, null, 2);
    } catch (err) {
      console.error("Failed to export collection:", err);
      return "[]";
    }
  }, [discs]);

  /**
   * Import collection from JSON string
   */
  const importCollection = useCallback(
    async (jsonData: string): Promise<InventoryOperationResult<Disc[]>> => {
      try {
        setOperationLoading(true);
        setOperationError(null);

        // Parse JSON data
        let importedDiscs: unknown;
        try {
          importedDiscs = JSON.parse(jsonData);
        } catch (parseErr) {
          const error = new InventoryError(InventoryErrorType.VALIDATION_ERROR, "Invalid JSON format", parseErr);
          return { success: false, error };
        }

        // Validate that it's an array
        if (!Array.isArray(importedDiscs)) {
          const error = new InventoryError(
            InventoryErrorType.VALIDATION_ERROR,
            "Imported data must be an array of discs"
          );
          return { success: false, error };
        }

        // Validate each disc
        const validationResults = importedDiscs.map((disc) => validateDiscData(disc));
        const invalidDiscs = validationResults.filter((result) => !result.success);

        if (invalidDiscs.length > 0) {
          const error = new InventoryError(
            InventoryErrorType.VALIDATION_ERROR,
            `${invalidDiscs.length} discs failed validation`,
            invalidDiscs.map((result) => result.error).filter(Boolean)
          );
          return { success: false, error };
        }

        // Extract valid discs
        const validDiscs = validationResults
          .filter((result) => result.success && result.data)
          .map((result) => result.data!);

        // Replace collection with imported discs
        const previousDiscs = discs;
        try {
          setDiscs(validDiscs);
          return { success: true, data: validDiscs };
        } catch (storageErr) {
          // Rollback on storage failure
          setDiscs(previousDiscs);
          throw storageErr;
        }
      } catch (err) {
        const error = new InventoryError(InventoryErrorType.OPERATION_FAILED, "Failed to import collection", err);
        setOperationError(error);
        return { success: false, error };
      } finally {
        setOperationLoading(false);
      }
    },
    [discs, setDiscs, validateDiscData]
  );

  return {
    discs,
    loading,
    error,
    addDisc,
    updateDisc,
    removeDisc,
    getDisc,
    addMultipleDiscs,
    removeMultipleDiscs,
    clearCollection,
    replaceCollection,
    getCollectionStats,
    exportCollection,
    importCollection,
    refresh,
    clearError,
  };
}
