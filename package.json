{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "test": "vitest --run", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:a11y": "vitest run --reporter=verbose __tests__/**/*.test.tsx", "audit:a11y": "node scripts/accessibility-audit.js", "audit:a11y:full": "pnpm build && pnpm start & sleep 10 && pnpm audit:a11y && kill %1", "lighthouse": "lighthouse http://localhost:3000 --output=json --output=html --output-path=./lighthouse-report", "lighthouse:ci": "lhci autorun", "perf:audit": "pnpm build && pnpm start & sleep 10 && pnpm lighthouse:ci && kill %1", "postinstall": "patch-package"}, "dependencies": {"@base-ui-components/react": "1.0.0-beta.2", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-tabs": "^1.1.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.539.0", "next": "15.4.6", "postcss": "^8.5.6", "react": "19.1.1", "react-dom": "19.1.1", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "web-vitals": "^5.1.0", "zod": "^4.0.17"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@lhci/cli": "^0.15.1", "@next/bundle-analyzer": "^15.4.6", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.2.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "@vitest/coverage-v8": "3.2.4", "axe-playwright": "^2.1.0", "eslint": "^9.33.0", "eslint-config-next": "15.4.6", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "lighthouse": "^12.8.1", "patch-package": "^8.0.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "vitest": "^3.2.4"}}