/**
 * Inventory Import Alternative Route for Disc Golf Inventory Management System
 *
 * This route provides an alternative URL (/inventory/import) that redirects to the main
 * import functionality at /import. This ensures navigation consistency and maintains
 * URL reliability for bookmarked links and empty state navigation.
 */

import { permanentRedirect } from "next/navigation";

/**
 * Inventory Import Alternative Page
 *
 * Redirects to the main import page to maintain consistent navigation
 * while supporting alternative URL patterns.
 */
export default function InventoryImportAlternativePage() {
  // Permanent redirect (308) to the main import page
  permanentRedirect("/import");
}

/**
 * Metadata configuration
 */
export const metadata = {
  title: "Import Collection - Disc Golf Inventory",
  description: "Import your disc golf collection data",
  robots: {
    index: false, // Prevent search engine indexing of redirect pages
    follow: true,
  },
};
