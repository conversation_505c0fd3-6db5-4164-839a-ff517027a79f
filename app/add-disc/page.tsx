/**
 * Add Disc Alternative Route for Disc Golf Inventory Management System
 *
 * This route provides an alternative URL (/add-disc) that redirects to the main
 * add disc functionality at /inventory/add. This ensures navigation consistency
 * and maintains URL reliability for bookmarked links.
 *
 * Requirements Satisfied:
 * - FR-ADDDISC-001: Redirect to /inventory/add for consistent navigation
 * - FR-ADDDISC-002: Maintain functionality for bookmarked /add-disc URLs
 */

import { permanentRedirect } from "next/navigation";

/**
 * Add Disc Alternative Page Component
 *
 * This page immediately redirects users to the main add disc page at /inventory/add.
 * Uses a 301 permanent redirect for SEO optimization and to indicate that this is
 * the canonical redirect behavior.
 *
 * @returns Never returns - always redirects
 */
export default function AddDiscAlternativePage() {
  // Permanent redirect (308) to the main add disc page
  // This tells search engines and browsers that /add-disc should always
  // redirect to /inventory/add, which is the canonical URL for this functionality
  permanentRedirect("/inventory/add");
}

/**
 * Metadata for the page (though users won't see it due to immediate redirect)
 */
export const metadata = {
  title: "Add Disc - Disc Golf Inventory",
  description: "Add a new disc to your disc golf collection",
  robots: {
    index: false, // Don't index this redirect page
    follow: true,
  },
};
