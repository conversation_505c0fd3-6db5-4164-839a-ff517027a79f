/**
 * Loading Component for Export Page
 *
 * Provides a loading state for the export page while data is being fetched.
 */

import { Layout, PageContainer } from "@/components/layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Download, Loader2 } from "lucide-react";

export default function ExportLoading() {
  return (
    <Layout>
      <PageContainer
        title="Export Collection"
        description="Preparing your export wizard..."
      >
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Loading Progress Indicator */}
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center justify-center w-10 h-10 rounded-full border-2 border-primary bg-primary text-primary-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
            </div>
            <div className="ml-3 hidden sm:block">
              <div className="text-sm font-medium text-foreground">Loading...</div>
              <div className="text-xs text-muted-foreground">Preparing export wizard</div>
            </div>
          </div>

          {/* Loading Card */}
          <Card className="max-w-2xl mx-auto">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <Download className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Loading Export Wizard</CardTitle>
              <CardDescription>
                Please wait while we prepare your collection for export
              </CardDescription>
            </CardHeader>
            <CardContent className="py-12">
              <div className="text-center space-y-4">
                <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Loading your disc collection...
                  </p>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div className="bg-primary h-2 rounded-full animate-pulse" style={{ width: "60%" }}></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageContainer>
    </Layout>
  );
}
