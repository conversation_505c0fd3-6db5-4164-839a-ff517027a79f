/**
 * Export Page for Disc Golf Inventory Management System
 *
 * A wizard-style interface for exporting disc collection data with format selection,
 * field customization, and progress indication.
 */

"use client";

import * as React from "react";
import { Layout, PageContainer } from "@/components/layout";
import { useInventory } from "@/hooks/useInventory";
import { ExportWizard } from "@/components/export/ExportWizard";

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * Export page component with wizard interface
 */
export default function ExportPage() {
  const { discs, loading: inventoryLoading } = useInventory();

  // Handlers
  const handleExportComplete = (data: string, filename: string) => {
    // This will be implemented when we add the actual export functionality
    console.log("Export completed:", { data: data.substring(0, 100) + "...", filename });
  };

  const handleCancel = () => {
    // Navigate back to inventory or home
    window.history.back();
  };

  // Show loading state while inventory is loading
  if (inventoryLoading) {
    return (
      <Layout>
        <PageContainer title="Export Collection" description="Export your disc golf collection data">
          <div className="flex items-center justify-center py-12">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-muted-foreground">Loading your collection...</p>
            </div>
          </div>
        </PageContainer>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageContainer
        title="Export Collection"
        description={`Export your disc golf collection (${discs.length} discs) to a file`}
      >
        <div className="max-w-4xl mx-auto">
          <ExportWizard discs={discs} onExportComplete={handleExportComplete} onCancel={handleCancel} />
        </div>
      </PageContainer>
    </Layout>
  );
}
