/**
 * Advanced Import Page for Disc Golf Inventory Management System
 *
 * A specialized page for importing from multiple sources including
 * files, URLs, clipboard data, and text input.
 */

"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { Layout, PageContainer } from "@/components/layout";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useInventory } from "@/hooks/useInventory";
import { AdvancedFileUploader } from "@/components/import/AdvancedFileUploader";
import { DataPreview } from "@/components/import/DataPreview";
import { MergeOptions } from "@/components/import/MergeOptions";
import { importFromJson, importFromCsv, type ImportResult } from "@/lib/exportImport";
import type { ImportSource } from "@/lib/advancedImportSources";
import type { Disc } from "@/lib/types";

// ============================================================================
// TYPES
// ============================================================================

type ImportStep = "source" | "preview" | "options" | "complete";

interface AdvancedImportState {
  step: ImportStep;
  selectedSource: ImportSource | null;
  importResult: ImportResult | null;
  isProcessing: boolean;
  error: string | null;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * Advanced Import Page Component
 *
 * Provides a comprehensive import wizard with support for multiple
 * import sources and advanced processing options.
 */
export default function AdvancedImportPage() {
  const router = useRouter();
  const { discs, addMultipleDiscs, replaceCollection } = useInventory();

  const [state, setState] = React.useState<AdvancedImportState>({
    step: "source",
    selectedSource: null,
    importResult: null,
    isProcessing: false,
    error: null,
  });

  // ============================================================================
  // HANDLERS
  // ============================================================================

  /**
   * Handle source selection
   */
  const handleSourceSelect = (source: ImportSource) => {
    setState((prev) => ({
      ...prev,
      selectedSource: source,
      error: null,
    }));
  };

  /**
   * Process the selected source
   */
  const handleProcessSource = async () => {
    if (!state.selectedSource) return;

    setState((prev) => ({ ...prev, isProcessing: true, error: null }));

    try {
      let content: string;

      // Get content based on source type
      if (state.selectedSource.data instanceof File) {
        content = await state.selectedSource.data.text();
      } else {
        content = state.selectedSource.data;
      }

      // Import based on format
      let result: ImportResult;
      if (state.selectedSource.format === "json") {
        result = importFromJson(content, discs);
      } else if (state.selectedSource.format === "csv") {
        result = importFromCsv(content, discs);
      } else {
        throw new Error("Unsupported file format");
      }

      setState((prev) => ({
        ...prev,
        importResult: result,
        step: result.success ? "preview" : "source",
        isProcessing: false,
        error: result.success ? null : result.error || "Import failed",
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        isProcessing: false,
        error: error instanceof Error ? error.message : "Import failed",
      }));
    }
  };

  /**
   * Handle import completion
   */
  const handleImportComplete = async (importedDiscs: Disc[], mergeStrategy: "replace" | "merge" | "append") => {
    try {
      if (mergeStrategy === "replace") {
        await replaceCollection(importedDiscs);
      } else {
        await addMultipleDiscs(importedDiscs);
      }

      setState((prev) => ({ ...prev, step: "complete" }));

      // Navigate back to inventory with success message
      setTimeout(() => {
        router.push("/inventory?import=advanced-success");
      }, 2000);
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to complete import",
      }));
    }
  };

  /**
   * Handle navigation back
   */
  const handleBack = () => {
    if (state.step === "source") {
      router.back();
    } else {
      setState((prev) => ({
        ...prev,
        step: prev.step === "preview" ? "source" : prev.step === "options" ? "preview" : "source",
        error: null,
      }));
    }
  };

  /**
   * Handle cancel
   */
  const handleCancel = () => {
    router.back();
  };

  // ============================================================================
  // RENDER STEP CONTENT
  // ============================================================================

  const renderStepContent = () => {
    switch (state.step) {
      case "source":
        return (
          <AdvancedFileUploader
            onSourceSelect={handleSourceSelect}
            selectedSource={state.selectedSource}
            isProcessing={state.isProcessing}
            error={state.error}
            onNext={handleProcessSource}
            onCancel={handleCancel}
          />
        );

      case "preview":
        return state.importResult ? (
          <DataPreview
            importResult={state.importResult}
            onNext={() => setState((prev) => ({ ...prev, step: "options" }))}
            onPrevious={() => setState((prev) => ({ ...prev, step: "source" }))}
            onCancel={handleCancel}
          />
        ) : null;

      case "options":
        return state.importResult?.data ? (
          <MergeOptions
            importedDiscs={state.importResult.data}
            currentDiscs={discs}
            onImportComplete={handleImportComplete}
            onPrevious={() => setState((prev) => ({ ...prev, step: "preview" }))}
            onCancel={handleCancel}
          />
        ) : null;

      case "complete":
        return (
          <div className="text-center space-y-4">
            <div className="text-2xl font-bold text-green-600">Import Complete!</div>
            <p className="text-muted-foreground">Your disc collection has been successfully imported.</p>
            <Button onClick={() => router.push("/inventory")}>View Collection</Button>
          </div>
        );

      default:
        return null;
    }
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  const getStepTitle = () => {
    switch (state.step) {
      case "source":
        return "Select Import Source";
      case "preview":
        return "Preview Import Data";
      case "options":
        return "Import Options";
      case "complete":
        return "Import Complete";
      default:
        return "Advanced Import";
    }
  };

  const getStepDescription = () => {
    switch (state.step) {
      case "source":
        return "Choose how you want to import your disc golf collection data";
      case "preview":
        return "Review the data that will be imported";
      case "options":
        return "Choose how to merge the imported data with your existing collection";
      case "complete":
        return "Your import has been completed successfully";
      default:
        return "Import your disc golf collection from multiple sources";
    }
  };

  return (
    <Layout>
      <PageContainer
        title={getStepTitle()}
        description={getStepDescription()}
        actions={
          state.step !== "complete" && (
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {state.step === "source" ? "Back" : "Previous"}
            </Button>
          )
        }
      >
        {renderStepContent()}
      </PageContainer>
    </Layout>
  );
}
