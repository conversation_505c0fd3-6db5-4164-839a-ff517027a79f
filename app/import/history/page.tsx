/**
 * Import History Page for Disc Golf Inventory Management System
 *
 * A dedicated page for viewing import history, managing operations,
 * and implementing undo/redo functionality.
 */

"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { Layout, PageContainer } from "@/components/layout";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useInventory } from "@/hooks/useInventory";
import { ImportHistoryManager } from "@/components/import/ImportHistoryManager";
import type { ImportOperation } from "@/lib/importHistory";

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * Import History Page Component
 *
 * Provides a comprehensive interface for viewing import history,
 * managing operations, and implementing undo/redo functionality.
 */
export default function ImportHistoryPage() {
  const router = useRouter();
  const { replaceCollection } = useInventory();

  // ============================================================================
  // HANDLERS
  // ============================================================================

  /**
   * Handle undo completion
   */
  const handleUndoComplete = async (operation: ImportOperation) => {
    try {
      if (operation.snapshot?.beforeData) {
        await replaceCollection(operation.snapshot.beforeData);
        // Show success message or redirect
        router.push("/inventory?undo=success");
      }
    } catch (error) {
      console.error("Failed to restore collection:", error);
    }
  };

  /**
   * Handle navigation back
   */
  const handleBack = () => {
    router.back();
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <Layout>
      <PageContainer
        title="Import History"
        description="Track and manage your import operations with undo/redo functionality"
        actions={
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        }
      >
        <ImportHistoryManager onUndoComplete={handleUndoComplete} />
      </PageContainer>
    </Layout>
  );
}
