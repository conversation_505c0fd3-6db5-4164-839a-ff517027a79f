/**
 * Import Templates Page for Disc Golf Inventory Management System
 *
 * A dedicated page for managing field mapping templates and presets
 * for CSV imports with full CRUD functionality.
 */

"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { Layout, PageContainer } from "@/components/layout";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { TemplateManager } from "@/components/import/TemplateManager";

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * Import Templates Page Component
 *
 * Provides a comprehensive interface for managing field mapping templates
 * and presets with create, edit, delete, and import/export functionality.
 */
export default function ImportTemplatesPage() {
  const router = useRouter();

  // ============================================================================
  // HANDLERS
  // ============================================================================

  /**
   * Handle navigation back
   */
  const handleBack = () => {
    router.back();
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <Layout>
      <PageContainer
        title="Import Templates"
        description="Manage field mapping presets for CSV imports"
        actions={
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        }
      >
        <TemplateManager />
      </PageContainer>
    </Layout>
  );
}
