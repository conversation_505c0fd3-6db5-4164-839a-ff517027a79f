/**
 * Import Page for Disc Golf Inventory Management System
 *
 * A wizard-style interface for importing disc collection data with file upload,
 * validation, preview, and merge options.
 *
 * Requirements Satisfied:
 * - FR-IMPORT-001: Accept JSON and CSV files with validation and error reporting
 * - FR-IMPORT-002: Provide clear error messages and suggestions for correction
 * - FR-IMPORT-003: Offer merge or replace options with preview functionality
 */

"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { Layout, PageContainer } from "@/components/layout";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Upload, Bookmark, Zap, History } from "lucide-react";
import Link from "next/link";
import { useInventory } from "@/hooks/useInventory";
import { ImportWizard } from "@/components/import/ImportWizard";
import type { Disc } from "@/lib/types";

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * Import Page Component
 *
 * Provides a comprehensive import wizard for disc collection data with support
 * for JSON and CSV files, validation, preview, and merge options.
 */
export default function ImportPage() {
  const router = useRouter();
  const { discs, addMultipleDiscs, replaceCollection } = useInventory();

  // ============================================================================
  // HANDLERS
  // ============================================================================

  /**
   * Handle successful import completion
   */
  const handleImportComplete = async (importedDiscs: Disc[], mergeStrategy: "replace" | "merge" | "append") => {
    try {
      if (mergeStrategy === "replace") {
        await replaceCollection(importedDiscs);
      } else {
        // Both "merge" and "append" use addMultipleDiscs
        // The difference is handled in the wizard's data preparation
        await addMultipleDiscs(importedDiscs);
      }

      // Navigate back to inventory with success message
      router.push("/inventory?import=success");
    } catch (error) {
      console.error("Import failed:", error);
      // Error handling is managed by the wizard
    }
  };

  /**
   * Handle import cancellation
   */
  const handleCancel = () => {
    router.back();
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <Layout>
      <PageContainer
        title="Import Collection"
        description="Import your disc golf collection from JSON or CSV files"
        actions={
          <div className="flex space-x-2">
            <Link href="/import/history">
              <Button variant="outline">
                <History className="h-4 w-4 mr-2" />
                History
              </Button>
            </Link>
            <Link href="/import/advanced">
              <Button variant="outline">
                <Zap className="h-4 w-4 mr-2" />
                Advanced
              </Button>
            </Link>
            <Link href="/import/templates">
              <Button variant="outline">
                <Bookmark className="h-4 w-4 mr-2" />
                Templates
              </Button>
            </Link>
            <Link href="/import/batch">
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                Batch Import
              </Button>
            </Link>
            <Button variant="outline" onClick={handleCancel}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
        }
      >
        <ImportWizard currentDiscs={discs} onImportComplete={handleImportComplete} onCancel={handleCancel} />
      </PageContainer>
    </Layout>
  );
}
