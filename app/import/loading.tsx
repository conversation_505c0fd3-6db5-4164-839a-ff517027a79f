/**
 * Loading Component for Import Page
 *
 * Provides a loading state for the import page while components are being loaded.
 */

import { Layout, PageContainer } from "@/components/layout";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Upload } from "lucide-react";

export default function ImportLoading() {
  return (
    <Layout>
      <PageContainer
        title="Import Collection"
        description="Import your disc golf collection from JSON or CSV files"
      >
        <div className="space-y-8">
          {/* Progress Indicator Skeleton */}
          <div className="flex items-center justify-center space-x-4">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="flex items-center space-x-2">
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-4 w-20" />
                {step < 4 && <Skeleton className="h-px w-8" />}
              </div>
            ))}
          </div>

          {/* Main Content Skeleton */}
          <Card>
            <CardContent className="p-8">
              <div className="text-center space-y-6">
                <Upload className="h-16 w-16 mx-auto text-muted-foreground animate-pulse" />
                <div className="space-y-2">
                  <Skeleton className="h-6 w-48 mx-auto" />
                  <Skeleton className="h-4 w-64 mx-auto" />
                </div>
                <div className="space-y-3">
                  <Skeleton className="h-32 w-full rounded-lg" />
                  <div className="flex justify-center space-x-3">
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-10 w-24" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageContainer>
    </Layout>
  );
}
