/**
 * Batch Import Page for Disc Golf Inventory Management System
 *
 * A specialized page for importing multiple files simultaneously with
 * parallel processing and consolidated results.
 */

"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { Layout, PageContainer } from "@/components/layout";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useInventory } from "@/hooks/useInventory";
import { BatchImportWizard } from "@/components/import/BatchImportWizard";
import type { Disc } from "@/lib/types";

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * Batch Import Page Component
 *
 * Provides a comprehensive batch import wizard for importing multiple
 * disc collection files simultaneously with parallel processing.
 */
export default function BatchImportPage() {
  const router = useRouter();
  const { discs, addMultipleDiscs, replaceCollection } = useInventory();

  // ============================================================================
  // HANDLERS
  // ============================================================================

  /**
   * Handle successful batch import completion
   */
  const handleImportComplete = async (importedDiscs: Disc[], mergeStrategy: "replace" | "merge" | "append") => {
    try {
      if (mergeStrategy === "replace") {
        await replaceCollection(importedDiscs);
      } else {
        // Both "merge" and "append" use addMultipleDiscs
        // The difference is handled in the batch processing
        await addMultipleDiscs(importedDiscs);
      }
      
      // Navigate back to inventory with success message
      router.push("/inventory?import=batch-success");
    } catch (error) {
      console.error("Batch import failed:", error);
      // Error handling is managed by the wizard
    }
  };

  /**
   * Handle batch import cancellation
   */
  const handleCancel = () => {
    router.back();
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <Layout>
      <PageContainer
        title="Batch Import Collection"
        description="Import multiple disc golf collection files simultaneously with parallel processing"
        actions={
          <Button variant="outline" onClick={handleCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        }
      >
        <BatchImportWizard
          currentDiscs={discs}
          onImportComplete={handleImportComplete}
          onCancel={handleCancel}
        />
      </PageContainer>
    </Layout>
  );
}
